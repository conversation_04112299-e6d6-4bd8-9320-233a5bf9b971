import { Drawer } from "vaul";
import type { BottomSheetProps } from "./types";

export default function BottomSheet({
  trigger,
  children,
  header,
  footer,
  ...rootProps
}: BottomSheetProps) {
  return (
    // @ts-expect-error vaul's types are wrong
    <Drawer.Root {...rootProps}>
      {trigger && <Drawer.Trigger asChild>{trigger}</Drawer.Trigger>}

      <Drawer.Portal>
        <Drawer.Title />
        <Drawer.Overlay className="bg-black-50 fixed inset-0 z-40" />

        <Drawer.Content className="fixed inset-x-0 bottom-0 z-50 flex flex-col rounded-t-xl bg-white">
          <Drawer.Handle className="bg-black-20 my-4 h-1.5 w-24 rounded-full" />
          {header ? <Drawer.Title asChild>{header}</Drawer.Title> : null}
          <div className="max-h-[80dvh] flex-1 overflow-y-auto">
            <Drawer.Close asChild>
              <DrawerChildren>{children}</DrawerChildren>
            </Drawer.Close>
          </div>
          {footer ? <footer>{footer}</footer> : null}
        </Drawer.Content>
      </Drawer.Portal>
    </Drawer.Root>
  );
}

function DrawerChildren({
  children,
  onClick,
}: {
  children: BottomSheetProps["children"];
  onClick?: (e: Event) => unknown;
}) {
  return children?.({ dismiss: () => onClick!(new Event("click")) });
}
