import type { RadioGroupProps } from "./types";
import * as radio from "@zag-js/radio-group";
import { useMachine, normalizeProps } from "@zag-js/react";
import { RadioGroupContext } from "./context";
import clsx from "clsx";
import { useId } from "react";

export default function RadioGroup({
  children,
  value,
  name,
  disabled = false,
  orientation = "vertical",
  onChange,
  className,
  ...rest
}: RadioGroupProps) {
  const id = useId();

  const service = useMachine(radio.machine, {
    id,
    name,
    value,
    disabled,
    orientation,
    onValueChange: (details) => {
      if (onChange && details.value) {
        onChange(details.value);
      }
    },
  });

  const api = radio.connect(service, normalizeProps);

  const classes = clsx(
    "flex",
    {
      "flex-col": orientation === "vertical",
      "flex-row": orientation === "horizontal",
    },
    className
  );

  return (
    <div {...rest} {...api.getRootProps()} className={classes}>
      <RadioGroupContext.Provider value={api}>
        {children}
      </RadioGroupContext.Provider>
    </div>
  );
}
