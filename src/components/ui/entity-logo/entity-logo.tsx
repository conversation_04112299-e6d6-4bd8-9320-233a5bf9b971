import clsx from "clsx";
import type { EntityLogoProps } from "./types";

export default function EntityLogo({
  size = "large",
  color = "#FFFFFF",
  seamless = false,
  elevation = "md",
  url,
  className,
  whiteBorder = false,
  greyBorder = false,
  ...rest
}: EntityLogoProps) {
  const containerClasses = clsx(
    "flex justify-center items-center",
    {
      "w-16 h-16 rounded-xl": size === "xlarge",
      "w-14 h-14 rounded-xl": size === "large",
      "w-10 h-10 rounded-lg": size === "medium",
      "w-7 h-7 rounded-[8px]": size === "small",
      "shadow-md": elevation === "md",
      "border-[0.5px] border-black-5": !seamless,
      "border-[1.2px] border-white": seamless && size === "small",
      "border-[1.2px] border-black-10": seamless && size !== "small",
    },
    whiteBorder && "border-[1.2px] border-white",
    greyBorder && "border-[1px] border-black-5",
    className
  );

  const imageClasses = clsx("object-contain", {
    "w-10 h-10": size === "xlarge",
    "w-8 h-8": size === "large",
    "w-6 h-6": size === "medium",
    "w-4 h-4": size === "small",
  });

  return (
    <div
      style={{ backgroundColor: color }}
      className={containerClasses}
      {...rest}
    >
      <img decoding="sync" src={url} alt="" className={imageClasses} />
    </div>
  );
}
