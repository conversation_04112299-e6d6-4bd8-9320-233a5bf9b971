import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination } from "swiper/modules";
// @ts-expect-error Not typed well enough
import "swiper/css";
// @ts-expect-error Not typed well enough
import "swiper/css/pagination";
import { getNudgesQueryOptions } from "@/queries/nudges";
import { trackEvent } from "@/utils/analytics";
import NudgeCard from "./nudge-card";
import {
  type Nudge,
  type NudgeCategory,
  type NudgeActionType,
  updateNudgeAction,
} from "@/clients/gen/personalization_api";
import { createPortal } from "react-dom";

interface NudgesProps {
  pageUri: string;
}

function renderNudge(
  nudge: Nudge,
  pageUri: string,
  onDismiss: (nudgeId: string, category: NudgeCategory) => void
) {
  const nudgeData = JSON.parse(nudge.nudge);

  if (nudge.type === "CARD") {
    const destination = nudgeData.redirect_deeplink?.path;
    const ctaKey = nudgeData.cta_label;
    const titleKey = nudgeData.title_keys?.[0];
    const subtitleKey = nudgeData.sub_title_keys?.[0];
    const iconUrl = nudgeData.icon_url;

    return (
      <SwiperSlide key={nudge.nudgeId}>
        <div
          onLoad={() => {
            trackEvent("nudge_card_viewed", {
              nudge_id: nudge.nudgeId,
              category: nudge.category,
              pageUri: pageUri,
            });
          }}
        >
          <NudgeCard
            category={nudge.category}
            nudgeId={nudge.nudgeId}
            pageUri={pageUri}
            titleKey={titleKey}
            subtitleKey={subtitleKey}
            iconUrl={iconUrl}
            ctaKey={ctaKey}
            destination={destination}
            dismissible={nudgeData.dismissible}
            onDismiss={onDismiss}
          />
        </div>
      </SwiperSlide>
    );
  }
  return null;
}

export default function Nudges({ pageUri }: NudgesProps) {
  const queryClient = useQueryClient();
  const query = getNudgesQueryOptions(pageUri);
  const { data: nudgesData, isLoading, error } = useQuery(query);

  const dismissMutation = useMutation({
    mutationFn: updateNudgeAction,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: query.queryKey });
    },
  });

  const handleDismiss = (nudgeId: string, category: NudgeCategory) => {
    dismissMutation.mutate({
      nudgeId,
      actionType: "DISMISS" as NudgeActionType,
      category,
    });
  };

  if (isLoading || error || !nudgesData?.nudges?.length) {
    return null;
  }

  return createPortal(
    <Swiper
      modules={[Pagination]}
      pagination={{ clickable: true }}
      spaceBetween={0}
      slidesPerView={1}
      onSlideChange={(swiper) => {
        const nudge = nudgesData.nudges[swiper.activeIndex];
        trackEvent("nudge_card_viewed", {
          nudge_id: nudge.nudgeId,
          category: nudge.category,
          pageUri: pageUri,
        });
      }}
      className="rounded-t-lg border-t bg-white shadow-lg"
      style={
        {
          "--swiper-pagination-color": "#916cff",
          "--swiper-pagination-bullet-inactive-color": "#00000040",
        } as React.CSSProperties
      }
    >
      {nudgesData.nudges.map((nudge) =>
        renderNudge(nudge, pageUri, handleDismiss)
      )}
    </Swiper>,
    document.getElementById("nudges")!
  );
}
