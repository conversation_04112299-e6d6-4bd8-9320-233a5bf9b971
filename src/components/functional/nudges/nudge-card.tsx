import type { <PERSON><PERSON><PERSON>, NudgeCategory } from "@/clients/gen/personalization_api";
import useDataKey from "@/hooks/data-key";
import { trackEvent } from "@/utils/analytics";
import Anchor from "@/components/functional/anchor";
import Button from "@/components/ui/button/button";
import closeIcon from "@/assets/images/icons/close.svg";

interface NudgeCardProps {
  nudgeId: string;
  titleKey: DataKey;
  category: NudgeCategory;
  pageUri: string;
  subtitleKey?: DataKey;
  destination?: string;
  ctaKey?: DataKey;
  iconUrl?: string;
  dismissible?: boolean;
  onDismiss?: (nudgeId: string, category: NudgeCategory) => void;
}

export default function NudgeCard({
  titleKey,
  destination,
  ctaKey,
  iconUrl,
  dismissible,
  nudgeId,
  category,
  pageUri,
  subtitleKey,
  onDismiss,
}: NudgeCardProps) {
  const handleCardClick = () => {
    trackEvent("nudge_card_clicked", {
      nudge_id: nudgeId,
      category: category,
      pageUri: pageUri,
    });
  };

  const handleDismiss = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onDismiss?.(nudgeId, category);
  };

  const titleText = useDataKey(titleKey);
  const subtitleText = useDataKey(subtitleKey);
  const ctaText = useDataKey(ctaKey);

  return (
    <div className="flex items-center gap-3 p-3" onClick={handleCardClick}>
      {iconUrl && (
        <Anchor href={destination} className="flex-shrink-0">
          <img
            src={iconUrl}
            alt="Card icon"
            className="h-9"
            crossOrigin="anonymous"
          />
        </Anchor>
      )}
      <Anchor
        className="flex flex-1 flex-col justify-center self-stretch"
        href={destination}
      >
        {titleKey && (
          <h4 className="text-heading4 line-clamp-2 font-medium">
            {titleText}
          </h4>
        )}
        {subtitleKey && (
          <h4 className="text-body2 text-black-50">{subtitleText}</h4>
        )}
      </Anchor>
      {ctaKey && (
        <Button className="flex-shrink-0" href={destination} size="small">
          {ctaText}
        </Button>
      )}
      {dismissible && (
        <button
          className="block flex-shrink-0"
          type="button"
          onClick={handleDismiss}
        >
          <img src={closeIcon} alt="Close" className="size-4" />
        </button>
      )}
    </div>
  );
}
