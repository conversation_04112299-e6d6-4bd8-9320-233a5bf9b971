import { useQuery } from "@tanstack/react-query";
import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination } from "swiper/modules";
// @ts-expect-error Not typed well enough
import "swiper/css";
// @ts-expect-error Not typed well enough
import "swiper/css/pagination";
import { getNudgesQueryOptions } from "@/queries/nudges";
import { trackEvent } from "@/utils/analytics";
import NudgeCard from "./nudge-card";
import { type Nudge } from "@/clients/gen/personalization_api";
import { useUserQuery } from "@/hooks/user";

interface NudgesProps {
  pageUri: string;
}

function renderNudge(nudge: Nudge, pageUri: string) {
  const nudgeData = JSON.parse(nudge.nudge);

  if (nudge.type === "CARD") {
    const destination = nudgeData.redirect_deeplink?.path;
    const ctaKey = nudgeData.cta_label;
    const titleKey = nudgeData.title_keys?.[0];
    const subtitleKey = nudgeData.sub_title_keys?.[0];
    const iconUrl = nudgeData.icon_url;

    return (
      <SwiperSlide key={nudge.nudgeId}>
        <div
          onLoad={() => {
            trackEvent("nudge_card_viewed", {
              nudge_id: nudge.nudgeId,
              category: nudge.category,
              pageUri: pageUri,
            });
          }}
        >
          <NudgeCard
            category={nudge.category}
            nudgeId={nudge.nudgeId}
            pageUri={pageUri}
            titleKey={titleKey}
            subtitleKey={subtitleKey}
            iconUrl={iconUrl}
            ctaKey={ctaKey}
            destination={destination}
          />
        </div>
      </SwiperSlide>
    );
  }
  return null;
}

export default function Nudges({ pageUri }: NudgesProps) {
  const query = getNudgesQueryOptions(pageUri);
  const { data: nudgesData, isLoading, error } = useQuery(query);
  const { data: profileData } = useUserQuery();
  if (
    profileData?.currentStatus === "NEW_USER" ||
    profileData?.currentStatus === "NOT_REGISTERED" ||
    profileData?.currentStatus === "LIFETIME_STATUS_UNKNOWN"
  ) {
    return (
      <div className="p-[3px]">
        <div className="rounded-t-lg bg-[#F1F1FF]">
          <NudgeCard
            category="BOND_JOURNEY"
            nudgeId="KYC_NOT_INITIATED"
            pageUri={pageUri}
            titleKey={{ key: "broking.nudge.title.kyc_not_initiated.1" }}
            subtitleKey={{ key: "broking.nudge.subtitle.kyc_not_initiated.1" }}
            iconUrl="https://assets.stablemoney.in/web-frontend/stable-bonds/person-scan.webp"
            ctaKey={{ key: "broking.nudge.cta.kyc_not_initiated.1" }}
            destination={"/onboarding/kyc"}
          />
        </div>
      </div>
    );
  }

  if (isLoading || error || !nudgesData?.nudges?.length) {
    return null;
  }

  return (
    <div className="p-0.75">
      <Swiper
        modules={[Pagination]}
        pagination={{ clickable: true }}
        spaceBetween={0}
        slidesPerView={1}
        onSlideChange={(swiper) => {
          const nudge = nudgesData.nudges[swiper.activeIndex];
          trackEvent("nudge_card_viewed", {
            nudge_id: nudge.nudgeId,
            category: nudge.category,
            pageUri: pageUri,
          });
        }}
        className="rounded-t-lg bg-[#F1F1FF]"
        style={
          {
            "--swiper-pagination-color": "#916cff",
            "--swiper-pagination-bullet-inactive-color": "#00000040",
          } as React.CSSProperties
        }
      >
        {nudgesData.nudges.map((nudge) => renderNudge(nudge, pageUri))}
      </Swiper>
    </div>
  );
}
