import type { <PERSON><PERSON><PERSON>, NudgeCategory } from "@/clients/gen/personalization_api";
import { trackEvent } from "@/utils/analytics";
import Anchor from "@/components/functional/anchor";
import Button from "@/components/ui/button/button";
import useData<PERSON>ey from "@/hooks/data-key";

interface NudgeCardProps {
  nudgeId: string;
  titleKey: DataKey;
  category: NudgeCategory;
  pageUri: string;
  subtitleKey?: DataKey;
  destination?: string;
  ctaKey?: DataKey;
  iconUrl?: string;
}

export default function NudgeCard({
  titleKey,
  destination,
  ctaKey,
  iconUrl,
  nudgeId,
  category,
  pageUri,
  subtitleKey,
}: NudgeCardProps) {
  const handleCardClick = () => {
    trackEvent("nudge_card_clicked", {
      nudge_id: nudgeId,
      category: category,
      pageUri: pageUri,
    });
  };

  const titleText = useDataKey(titleKey);
  const subtitleText = useDataKey(subtitleKey);
  const ctaText = useDataKey(ctaKey);

  return (
    <div
      className="relative flex flex-col gap-8 px-6 py-8"
      onClick={handleCardClick}
    >
      <div className="flex items-center gap-3">
        {iconUrl && (
          <Anchor href={destination} className="flex-shrink-0">
            <img
              src={iconUrl}
              alt="Card icon"
              className="h-12"
              crossOrigin="anonymous"
            />
          </Anchor>
        )}
        <Anchor
          className="flex flex-col justify-center self-stretch"
          href={destination}
        >
          {titleKey && (
            <h4 className="text-body1 line-clamp-2 font-medium">{titleText}</h4>
          )}
          {subtitleKey && (
            <h4 className="text-body1 text-black-50">{subtitleText}</h4>
          )}
        </Anchor>
      </div>
      {ctaKey && (
        <Button href={destination}>
          <p className="text-body1 leading-0">{ctaText}</p>
        </Button>
      )}
    </div>
  );
}
