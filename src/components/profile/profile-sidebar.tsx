import { useLocation } from "react-router";
import clsx from "clsx";
import Anchor from "../functional/anchor";
import { logout } from "@/clients/auth";
import useNavigate from "@/hooks/navigate";

const menusData = [
  { label: "User Details", link: "/profile" },
  { label: "Bank", link: "/profile/bank" },
  { label: "Demat", link: "/profile/demat" },
  { label: "KYC", link: "/profile/kyc" },
  { label: "Reports", link: "/profile/reports" },
];

export default function ProfileSidebar() {
  const location = useLocation();
  const navigate = useNavigate();
  const handleLogout = () => {
    logout();
    navigate("/", { replace: true });
  };

  return (
    <aside>
      <div className="mb-4 flex flex-row md:flex-col md:space-y-4">
        {menusData.map((menu) => (
          <Anchor
            key={menu.label}
            href={menu.link}
            className={clsx(
              "text-body1 md:text-heading4 rounded-sm px-4 py-2 font-medium",
              location.pathname === menu.link && "bg-purple/10",
              location.pathname !== menu.link && "hover:bg-purple/5"
            )}
          >
            {menu.label}
          </Anchor>
        ))}
        <div className="hidden h-[1px] w-full bg-[#EBEBEB] sm:flex" />
        <Anchor
          onClick={handleLogout}
          className="text-body1 md:text-heading4 text-black-80 hover:bg-purple/5 hidden rounded-sm px-4 py-2 font-medium sm:flex"
        >
          <p>Sign out</p>
        </Anchor>
      </div>
    </aside>
  );
}
