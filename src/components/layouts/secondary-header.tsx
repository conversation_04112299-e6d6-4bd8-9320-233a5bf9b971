import Button from "@/components/ui/button/button";
import clsx from "clsx";
import Anchor from "../functional/anchor";
import { useUserQuery } from "@/hooks/user";
import greenShieldTick from "@/assets/images/icons/green-shield-tick.svg";
import passbook from "@/assets/images/icons/passbook.svg";

interface SecondaryHeaderProps {
  hideHeader?: boolean;
  className?: string;
}

export default function SecondaryHeader({
  hideHeader = false,
  className,
}: SecondaryHeaderProps) {
  const userQuery = useUserQuery();
  const isLoggedIn = !!userQuery.data;

  return (
    <div
      className={clsx(
        "border-black-40 sticky top-0 z-40 flex h-16 w-full items-center justify-center border-b bg-white",
        hideHeader && "hidden md:flex",
        className
      )}
    >
      <div className="mx-auto flex w-full max-w-[1366px] items-center justify-between px-5">
        <div className="flex items-center space-x-3">
          <Anchor href="/">
            <img
              src="https://assets.stablemoney.in/web-frontend/stable_bonds_black_logo.webp"
              alt="logo"
              className="h-6 md:h-8"
            />
          </Anchor>
          <div className="hidden h-8 w-[1px] bg-[#EBEBEB] sm:flex" />
          <div className="hidden items-center space-x-1 sm:flex">
            <img src={greenShieldTick} alt="Green Shield Tick" />
            <span className="text-black-50 text-[15px]">
              SEBI registered platform{" "}
            </span>
          </div>
        </div>
        {!isLoggedIn ? (
          <Button size="small" href="/authentication/mobile-number">
            Login/Register
          </Button>
        ) : (
          <div className="flex items-center gap-8">
            <Anchor
              className={clsx(
                "text-black-80 flex items-center gap-1 rounded-sm px-4 py-2 text-[15px]",
                location.pathname === "/investments" && "bg-purple/10",
                location.pathname !== "/investments" && "hover:bg-purple/5"
              )}
              href="/investments"
            >
              <img src={passbook} alt="Passbook" />
              Passbook
            </Anchor>
            <Anchor href="/profile">
              <img
                src="https://assets.stablemoney.in/app/profile-cta.webp "
                alt="profile"
                className="size-6 sm:size-8"
              />
            </Anchor>
          </div>
        )}
      </div>
    </div>
  );
}
