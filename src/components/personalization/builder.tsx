import type { Widget } from "@/clients/gen/personalization_api";
import { buildBasePage } from "./widgets/base-page";
import { buildBondDetails } from "./widgets/bond-details";
import BondInvestmentsSummary from "./widgets/bond-investments-summary";
import BondsCollectionTable from "./widgets/bonds-collection-table";
import BondsCollection from "./widgets/bonds-collection";
import BondsDataTable from "./widgets/bonds-data-table";
import { buildCard } from "./widgets/card";
import BondsDisclosureButton from "./widgets/bonds-disclosure-button";
import BondsDisclosureDetails from "./widgets/bonds-disclosure-details";
import BondsDisclosureIssuerDetails from "./widgets/bonds-disclosure-issuer-details";
import { buildBondsHeroHeader } from "./widgets/bonds-hero-header";
import BondsInvestButton from "./widgets/bonds-invest-button";
import BondsKeyHighlights from "./widgets/bonds-key-highlights";
import BondsMarketingHighlights from "./widgets/bonds-marketing-highlights";
import BondsSellerEntityDisclosure from "./widgets/bonds-seller-entity-disclosure";
import { buildBondsSingleMedia } from "./widgets/bonds-single-media";
import BondsSupportedDocuments from "./widgets/bonds-supported-documents";
import BondsTnCAndKYCFooter from "./widgets/bonds-tnc-and-kyc-footer";
import BulletPoints from "./widgets/bullet-points";
import { buildCarouselItem } from "./widgets/carousel-item";
import { buildColumn } from "./widgets/column";
import { buildCarousel } from "./widgets/carousel";
import DataTable from "./widgets/data-table";
import Faqs from "./widgets/faqs";
import { buildHorizontalCollectionItem } from "./widgets/horizontal-collection-item";
import { buildHorizontalCollectionSection } from "./widgets/horizontal-collection-section";
import { buildHorizontalListSection } from "./widgets/horizontal-list-section";
import Image from "./widgets/image";
import InlineFaqs from "./widgets/inline-faqs";
import { buildMarketingListWithCover } from "./widgets/marketing-list-with-cover";
import SingleCategoryFAQ from "./widgets/single-category-faq";
import { buildAnimation } from "./widgets/animation";
import { buildAppBar } from "./widgets/app-bar";
import { buildClippedTransitionCarousel } from "./widgets/clipped-transition-carousel";
import BondsReturnsCalculator from "./widgets/bonds-returns-calculator";
import BondsHeroCollection from "./widgets/bonds-hero-collection";
import BondsExpandableCollection from "./widgets/bonds-expandable-collection";

export function buildPersonalizationWidget(widget: Widget) {
  switch (widget.widget) {
    case "Animation":
      return buildAnimation(widget);
    case "AppBar":
      return buildAppBar(widget);
    case "ClippedTransitionCarousel":
      return buildClippedTransitionCarousel(widget);
    case "BasePage":
      return buildBasePage(widget);
    // BondDetailsAppBar removed - not a valid widget type
    case "BondDetails":
      return buildBondDetails(widget);
    case "BondInvestmentsSummary":
      return <BondInvestmentsSummary key={widget.id} />;
    case "Image":
      return <Image {...widget.props} id={widget.id} key={widget.id} />;
    case "Card":
      return buildCard(widget);
    case "Carousel":
      return buildCarousel(widget);
    case "CarouselItem":
      return buildCarouselItem(widget);
    case "Column":
      return buildColumn(widget);
    case "HorizontalListSection":
      return buildHorizontalListSection(widget);
    case "MarketingListWithCover":
      return buildMarketingListWithCover(widget);
    case "BondsHeroHeader":
      return buildBondsHeroHeader(widget);
    case "BondsCollection":
      return (
        <BondsCollection {...widget.props} id={widget.id} key={widget.id} />
      );
    case "BondsCollectionTable":
      return (
        <BondsCollectionTable
          {...widget.props}
          id={widget.id}
          key={widget.id}
        />
      );
    case "SingleCategoryFAQ":
      return (
        <SingleCategoryFAQ {...widget.props} id={widget.id} key={widget.id} />
      );
    case "BulletPoints":
      return <BulletPoints {...widget.props} id={widget.id} key={widget.id} />;
    case "BondsKeyHighlights":
      return (
        <BondsKeyHighlights {...widget.props} id={widget.id} key={widget.id} />
      );
    case "BondsMarketingHighlights":
      return (
        <BondsMarketingHighlights
          {...widget.props}
          id={widget.id}
          key={widget.id}
        />
      );
    case "BondsSupportedDocuments":
      return (
        <BondsSupportedDocuments
          {...widget.props}
          id={widget.id}
          key={widget.id}
        />
      );
    case "BondsDisclosureButton":
      return (
        <BondsDisclosureButton
          {...widget.props}
          id={widget.id}
          key={widget.id}
        />
      );
    case "BondsInvestButton":
      return (
        <BondsInvestButton {...widget.props} id={widget.id} key={widget.id} />
      );
    case "BondsReturnsCalculator":
      return (
        <BondsReturnsCalculator
          {...widget.props}
          id={widget.id}
          key={widget.id}
        />
      );
    case "BondsTnCAndKYCFooter":
      return (
        <BondsTnCAndKYCFooter
          {...widget.props}
          id={widget.id}
          key={widget.id}
        />
      );
    case "BondsDisclosureDetails":
      return (
        <BondsDisclosureDetails
          {...widget.props}
          id={widget.id}
          key={widget.id}
        />
      );
    case "BondsDisclosureIssuerDetails":
      return (
        <BondsDisclosureIssuerDetails
          {...widget.props}
          id={widget.id}
          key={widget.id}
        />
      );
    case "BondsSellerEntityDisclosure":
      return (
        <BondsSellerEntityDisclosure
          {...widget.props}
          id={widget.id}
          key={widget.id}
        />
      );
    case "BondsSingleMedia":
      return buildBondsSingleMedia(widget);
    case "BondsDataTable":
      return (
        <BondsDataTable {...widget.props} id={widget.id} key={widget.id} />
      );
    case "DataTable":
      return <DataTable {...widget.props} id={widget.id} key={widget.id} />;
    case "InlineFaqs":
      return <InlineFaqs {...widget.props} id={widget.id} key={widget.id} />;
    case "Faqs":
      return <Faqs {...widget.props} id={widget.id} key={widget.id} />;
    case "HorizontalCollectionSection":
      return buildHorizontalCollectionSection(widget);
    case "HorizontalCollectionItem":
      return buildHorizontalCollectionItem(widget);
    case "BondsHeroCollection":
      return (
        <BondsHeroCollection {...widget.props} id={widget.id} key={widget.id} />
      );
    case "BondsExpandableCollection":
      return (
        <BondsExpandableCollection
          {...widget.props}
          id={widget.id}
          key={widget.id}
        />
      );
    default:
      return (
        <p className="text-red" key={widget.id}>
          Unsupported widget type {widget.widget} with id {widget.id}
        </p>
      );
  }
}
