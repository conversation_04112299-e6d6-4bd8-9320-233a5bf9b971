import { useState, useRef, type HTMLAttributes } from "react";
import { useQuery } from "@tanstack/react-query";
import SectionHeading from "@/components/ui/section-heading/section-heading";
import QueryRenderer from "@/components/functional/query-renderer";
import { createCollectionQueryOptions } from "@/queries/bonds";
import { type BondsExpandableCollectionProps } from "@/clients/gen/personalization_api";
import StandardBondCard from "@/components/bonds/standard-bond-card";
import ViewMoreButton from "@/components/bonds/view-more-button";

type BondsExpandableCollectionComponentProps = BondsExpandableCollectionProps &
  HTMLAttributes<HTMLDivElement>;

export default function BondsExpandableCollection({
  collectionName,
  viewAllMinLimit,
  ...rest
}: BondsExpandableCollectionComponentProps) {
  const query = useQuery(createCollectionQueryOptions(collectionName));
  const loader = (
    <div className="animate-pulse space-y-6 p-5">
      <div className="bg-black-5 h-5 w-48 rounded-lg"></div>
      <div className="space-y-4">
        <div className="bg-black-5 inline-block h-[161px] w-full rounded-xl"></div>
        <div className="bg-black-5 inline-block h-[161px] w-full rounded-xl"></div>
        <div className="bg-black-5 inline-block h-[161px] w-full rounded-xl"></div>
      </div>
    </div>
  );
  const [showAll, setShowAll] = useState(false);
  const viewMoreButtonRef = useRef<HTMLDivElement>(null);

  const toggleCards = () => {
    if (showAll) {
      viewMoreButtonRef.current?.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });

      setTimeout(() => {
        setShowAll(!showAll);
      }, 500);
    } else {
      setShowAll(!showAll);
    }
  };

  return (
    <div {...rest} data-widget="BondsCollection">
      <QueryRenderer query={query} loader={loader}>
        {(collection) => (
          <div className="space-y-6 text-center" ref={viewMoreButtonRef}>
            <SectionHeading size="small" separator title={collection.title} />
            <div className="space-y-4">
              {collection.collectionItem
                .slice(
                  0,
                  viewAllMinLimit
                    ? showAll
                      ? collection.collectionItem.length
                      : viewAllMinLimit
                    : collection.collectionItem.length
                )
                .map((item) => (
                  <div key={item.id}>
                    <StandardBondCard item={item} />
                  </div>
                ))}
            </div>
            {viewAllMinLimit &&
              collection.collectionItem.length > viewAllMinLimit && (
                <ViewMoreButton
                  isExpanded={showAll}
                  handleClick={toggleCards}
                />
              )}
          </div>
        )}
      </QueryRenderer>
    </div>
  );
}
