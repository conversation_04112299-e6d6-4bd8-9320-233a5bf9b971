import Surface from "../ui/surface/surface";
import { useMemo, type HTMLAttributes } from "react";
import Anchor from "../functional/anchor";
import { makeBondUrl } from "@/utils/routing";
import { trackEvent } from "@/utils/analytics";
import { InvestabilityStatus } from "@/clients/gen/broking/BondDetails_pb";
import type { CollectionItem } from "@/clients/gen/broking/Collection_pb";
import chevron from "@/assets/images/icons/chevron.svg";
import { formatToInr } from "@/utils/number";
import ProgressBar from "../ui/progress-bar/progress-bar";
import { formatAmount } from "@/utils/format";
import EntityLogo from "../ui/entity-logo/entity-logo";
import Tag from "../ui/tag/tag";
import leftYtm from "@/assets/images/illustrations/left-ytm.webp";
import rightYtm from "@/assets/images/illustrations/right-ytm.webp";
import { useMediaQuery } from "@react-hook/media-query";
import redStrike from "@/assets/images/illustrations/red-strike.webp";
import GradientNumber from "../ui/gradient-number";
import Button from "../ui/button/button";
import HorizontalDivider from "../ui/horizontal-divider";

type CollectionItemProps = {
  item: CollectionItem;
  collectionName?: string;
  rank?: number;
  indexed?: boolean;
  className?: string;
} & HTMLAttributes<HTMLAnchorElement>;

function LogoTag({ item }: { item: CollectionItem }) {
  const isDesktop = useMediaQuery("(min-width: 768px)");

  return (
    <div className="relative z-20 pt-2.75 md:pt-8">
      <div className="flex items-center gap-2 pl-3 md:pl-8">
        {item?.aboutTheInstitution?.logo && (
          <EntityLogo
            size={isDesktop ? "xlarge" : "medium"}
            url={item?.aboutTheInstitution?.logo}
            color={"#FFFFFF"}
            elevation="none"
            greyBorder
          />
        )}

        {item.tagConfig &&
          (item.tagConfig?.name && item.tagConfig?.type === "tag" ? (
            <Tag
              color={item.tagConfig.color}
              backgroundColor={item.tagConfig.bgColor}
              shimmerColor="#916CFF"
              borderColor="#916CFF33"
              hasShimmer
            >
              {item.tagConfig?.iconUrl && (
                <img
                  decoding="sync"
                  src={item.tagConfig?.iconUrl}
                  className="mr-1 w-2"
                  alt="Icon"
                />
              )}
              <span className="whitespace-nowrap"> {item.tagConfig.name} </span>
            </Tag>
          ) : (
            <div className="mt-1 flex items-center">
              {item.tagConfig?.iconUrl && (
                <img
                  decoding="sync"
                  src={item.tagConfig?.iconUrl}
                  className="mr-1 w-2"
                  alt="Icon"
                />
              )}
              <span className="text-body2 text-black-50 line-clamp-1 text-ellipsis">
                {item.tagConfig?.name}
              </span>
            </div>
          ))}
      </div>
    </div>
  );
}

function YtmRate({ item }: { item: CollectionItem }) {
  return (
    <div className="absolute bottom-0 left-0 z-20 flex items-end">
      <img src={leftYtm} alt="left-ytm" className="h-9.5 md:h-16.25" />
      <p className="relative space-x-[4px] bg-white md:space-x-[6.8px] md:py-2">
        <span className="text-black-80 text-heading2 pl-1 font-medium md:pl-4">
          {formatAmount(item.xirr)}%
        </span>
        {item.struckenYield > 0 && (
          <span className="relative inline-block">
            <span className="text-black-40 text-body2 font-medium">
              {formatAmount(item.struckenYield)}%
            </span>
            <img
              src={redStrike}
              alt="red-strike"
              className="absolute top-3 left-0 z-20 w-6 md:w-11"
            />
          </span>
        )}
        <span className="text-black-50 text-body2">YTM</span>
      </p>
      <img src={rightYtm} alt="right-ytm" className="h-7 md:h-12" />
    </div>
  );
}

export default function FeaturedBondCard({
  item,
  collectionName = "",
  rank = 0,
  indexed,
  className,
  ...rest
}: CollectionItemProps) {
  const getSoldProgress = useMemo(
    () =>
      (progress: number): string => {
        if (progress < 50) {
          return "#12be57"; // Green
        } else if (progress >= 50 && progress < 80) {
          return "#F47126"; // Orange
        } else if (progress >= 80) {
          return "#EE4950"; // Red
        } else {
          return "#D9D9D9"; // Gray
        }
      },
    []
  );
  const handleBondsCollectionItemClicked = () => {
    trackEvent("bonds_collection_item_clicked", {
      bond_name: item.aboutTheInstitution?.bondInstitutionName,
      bond_type: InvestabilityStatus[item.investabilityStatus],
      bond_id: item.id,
      collectionName,
      rank,
    });
  };

  const mastHeadUrl = item?.mediaItems?.find(
    (mediaItem) => mediaItem.section === "MastHead"
  )?.url;

  return (
    <Anchor
      href={makeBondUrl(item)}
      className={className}
      onClick={handleBondsCollectionItemClicked}
      {...rest}
    >
      <Surface borderWidth="md">
        <div className="space-y-4 rounded-[9px] p-[3px] md:space-y-7 md:p-1">
          <div
            style={{ background: `${item.bgColor}` }}
            className="relative flex h-22 justify-between gap-5 overflow-hidden rounded-lg md:h-44"
          >
            <div
              style={{
                background: `linear-gradient(to right, ${item.bgColor} 0%, ${item.bgColor} 50%, ${item.bgColor}00 100%)`,
              }}
              className="absolute inset-0 z-10 rounded-lg"
            />
            <LogoTag item={item} />
            {mastHeadUrl && (
              <img
                src={mastHeadUrl}
                alt="MastHead"
                className="absolute top-0 right-0 h-full w-auto origin-top-right scale-160 transform rounded-lg object-cover"
              />
            )}
            <YtmRate item={item} />
            {indexed && (
              <GradientNumber
                value={rank}
                className="absolute right-4 -bottom-11 text-[108px] drop-shadow-[0_10px_25px_rgba(0,0,0,40)]"
              />
            )}
          </div>

          <div className="space-y-[14px] px-4 pb-4 md:space-y-5 md:px-9 md:pb-7">
            <div className="flex items-center gap-[6px] md:gap-3">
              <p className="text-black-80 text-body1 flex-shrink-0">
                {item.tenure}
              </p>
              <div className="bg-black-40 h-1 w-1 flex-shrink-0 rounded-full md:h-2 md:w-2"></div>
              <p className="text-black-40 text-body1 truncate">
                {item.displayTitle}
              </p>
              <img
                src={chevron}
                alt="chevron-right"
                className="w-[5px] md:w-[7px] md:pt-[1px]"
              />
            </div>
            <HorizontalDivider />
            <div className="flex items-center justify-between gap-2">
              <div className="flex h-7.5 items-center gap-[10px] md:h-12 md:gap-10">
                <div className="flex h-full flex-col justify-between">
                  <div className="text-black-40 text-body2">Min</div>
                  <div className="text-black-60 text-body1 md:tracking-[-0.18px]">
                    {formatToInr(item.minimumInvestment)}
                  </div>
                </div>
                <div className="bg-black-5 h-full w-[1px] md:w-[1.5px]" />
                <div className="flex h-full flex-col justify-between">
                  <div className="pt-[1px] md:pt-1">
                    <ProgressBar
                      progress={item.soldOutPercentage}
                      barColor={getSoldProgress(item.soldOutPercentage)}
                      bgColor="#D9D9D9"
                      className="mt-1"
                    />
                  </div>
                  <div className="text-black-60 text-body1 md:tracking-[-0.18px]">
                    {formatAmount(item.soldOutPercentage)}% sold
                  </div>
                </div>
              </div>
              <Button size="small" className="md:py-5">
                <span className="text-body1">Invest now </span>
              </Button>
            </div>
          </div>
        </div>
      </Surface>
    </Anchor>
  );
}
