import type { CollectionItem } from "@/clients/gen/broking/Collection_pb";
import { useMemo, type HTMLAttributes } from "react";
import chevron from "@/assets/images/icons/chevron.svg";
import EntityLogo from "../ui/entity-logo/entity-logo";
import Surface from "../ui/surface/surface";
import { formatAmount } from "@/utils/format";
import redStrike from "@/assets/images/illustrations/red-strike.webp";
import GradientNumber from "../ui/gradient-number";
import Anchor from "../functional/anchor";
import { makeBondUrl } from "@/utils/routing";
import { trackEvent } from "@/utils/analytics";
import { InvestabilityStatus } from "@/clients/gen/broking/BondDetails_pb";
import Button from "../ui/button/button";
import { formatToInr } from "@/utils/number";
import ProgressBar from "../ui/progress-bar/progress-bar";
import HorizontalDivider from "../ui/horizontal-divider";

type CollectionItemProps = {
  item: CollectionItem;
  collectionName?: string;
  indexed?: boolean;
  rank?: number;
  className?: string;
} & HTMLAttributes<HTMLAnchorElement>;

export default function StandardBondCard({
  item,
  rank = 0,
  indexed = false,
  className,
  collectionName = "",
  ...rest
}: CollectionItemProps) {
  const getSoldProgress = useMemo(
    () =>
      (progress: number): string => {
        if (progress < 50) {
          return "#12be57"; // Green
        } else if (progress >= 50 && progress < 80) {
          return "#F47126"; // Orange
        } else if (progress >= 80) {
          return "#EE4950"; // Red
        } else {
          return "#D9D9D9"; // Gray
        }
      },
    []
  );
  const handleBondsCollectionItemClicked = () => {
    trackEvent("bonds_collection_item_clicked", {
      bond_name: item.aboutTheInstitution?.bondInstitutionName,
      bond_type: InvestabilityStatus[item.investabilityStatus],
      bond_id: item.id,
      collectionName,
      rank,
    });
  };
  return (
    <Anchor
      href={makeBondUrl(item)}
      className={className}
      onClick={handleBondsCollectionItemClicked}
      {...rest}
    >
      <Surface elevation="md">
        <div className="relative flex gap-5 px-9 py-6">
          <div>
            <EntityLogo
              size="large"
              url={item.aboutTheInstitution?.logo || ""}
              color={item.bgColor}
              greyBorder
              elevation="none"
            />
          </div>
          <div className="flex w-full flex-col gap-4">
            <div className="flex w-full items-center justify-between">
              <div className="space-y-1">
                <p className="relative space-x-[4px] bg-white text-left md:space-x-[6.8px]">
                  <span className="text-black-80 text-heading3 font-medium">
                    {formatAmount(item.xirr)}%
                  </span>
                  {item.struckenYield > 0 && (
                    <span className="relative inline-block">
                      <span className="text-black-40 text-body2 font-medium">
                        {formatAmount(item.struckenYield)}%
                      </span>
                      <img
                        src={redStrike}
                        alt="red-strike"
                        className="absolute top-3 left-0 z-20 w-6 md:w-11"
                      />
                    </span>
                  )}
                  <span className="text-black-50 text-body2">YTM</span>
                </p>
                <div className="flex items-center gap-[6px] md:gap-3">
                  <p className="text-black-40 text-body1 truncate">
                    {item.displayTitle}
                  </p>
                  <img
                    src={chevron}
                    alt="chevron-right"
                    className="w-[5px] md:w-[7px] md:pt-[1px]"
                  />
                </div>
              </div>
              <Button size="small" className="md:py-5">
                <span className="text-body1">Invest now </span>
              </Button>
            </div>
            <HorizontalDivider />
            <div className="flex h-7.5 items-center gap-[10px] text-left md:h-12 md:gap-10">
              <div className="flex h-full flex-col justify-between">
                <div className="text-black-40 text-body2">Tenure</div>
                <div className="text-black-60 text-body1">{item.tenure}</div>
              </div>
              <div className="bg-black-5 h-full w-[1px]" />
              <div className="flex h-full flex-col justify-between">
                <div className="text-black-40 text-body2">Min</div>
                <div className="text-black-60 text-body1">
                  {formatToInr(item.minimumInvestment)}
                </div>
              </div>
              <div className="bg-black-5 h-full w-[1px]" />
              <div className="flex h-full flex-col justify-between gap-1.5">
                <div className="pt-[1px]">
                  <ProgressBar
                    progress={item.soldOutPercentage}
                    barColor={getSoldProgress(item.soldOutPercentage)}
                    bgColor="#D9D9D9"
                    className="mt-1"
                  />
                </div>
                <div className="text-black-60 text-body1">
                  {formatAmount(item.soldOutPercentage)}% sold
                </div>
              </div>
            </div>
          </div>
          {indexed && rank && (
            <GradientNumber
              value={rank}
              className="absolute right-3 -bottom-11 text-[104px]"
            />
          )}
        </div>
      </Surface>
    </Anchor>
  );
}
