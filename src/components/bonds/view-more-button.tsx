import Button from "../ui/button/button";
import Surface from "../ui/surface/surface";

export default function ViewMoreButton({
  isExpanded,
  handleClick,
}: {
  isExpanded: boolean;
  handleClick: () => void;
}) {
  return (
    <Surface borderWidth="md">
      <div className="flex justify-center">
        <Button onClick={handleClick} variant="secondary" className="py-5">
          <span className="text-body1">
            {isExpanded ? "View less" : "View all"}
          </span>
        </Button>
      </div>
    </Surface>
  );
}
