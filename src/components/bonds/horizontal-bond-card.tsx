import type { BondItem } from "@/clients/gen/broking/FilterSearch_pb";
import Tag from "@/components/ui/tag/tag";
import { formatAmount } from "@/utils/format";

const HorizontalBondsCard = ({ item }: { item: BondItem }) => {
  return (
    <div className="flex flex-col gap-4 px-5">
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-3">
          <div className="border-black-10 flex h-11 w-11 items-center justify-center rounded-sm border-1">
            <img
              decoding="sync"
              alt="institution logo"
              className="h-6 w-6 object-contain"
              src={item.logoUrl}
            />
          </div>
          <div className="flex flex-col gap-0.5">
            <div className="flex gap-1">
              <p className="text-heading4">{item.tenure}</p>
              {item.tag?.name && (
                <Tag
                  color={item.tag?.color}
                  backgroundColor={item.tag?.bgColor}
                >
                  {item.tag?.name}
                </Tag>
              )}
            </div>

            <div className="flex items-center gap-1">
              <p className="text-body2 text-black-50 max-w-[22ch] truncate">
                {item.displayTitle}
              </p>
            </div>
          </div>
        </div>
        <div className="flex flex-1 items-center justify-end gap-1">
          <div className="flex flex-col">
            <p className="text-green text-heading4 text-right font-medium">
              {formatAmount(item.ytm)}%
            </p>
            <p className="text-body2 text-black-40 text-right">{item.rating}</p>
          </div>

          <svg
            width="9"
            height="5"
            viewBox="0 0 9 5"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className={
              "block flex-shrink-0 -rotate-90 opacity-60 transition-transform duration-300"
            }
          >
            <path
              d="M8 0.75L4.5 4.25L1 0.75"
              stroke={"black"}
              strokeOpacity="0.8"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </div>
      </div>
    </div>
  );
};

export default HorizontalBondsCard;
