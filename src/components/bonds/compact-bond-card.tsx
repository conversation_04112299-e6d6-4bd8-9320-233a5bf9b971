import type { CollectionItem } from "@/clients/gen/broking/Collection_pb";
import type { HTMLAttributes } from "react";
import chevron from "@/assets/images/icons/chevron.svg";
import EntityLogo from "../ui/entity-logo/entity-logo";
import Surface from "../ui/surface/surface";
import { formatAmount } from "@/utils/format";
import redStrike from "@/assets/images/illustrations/red-strike.webp";
import GradientNumber from "../ui/gradient-number";
import Anchor from "../functional/anchor";
import { makeBondUrl } from "@/utils/routing";
import { trackEvent } from "@/utils/analytics";
import { InvestabilityStatus } from "@/clients/gen/broking/BondDetails_pb";

type CollectionItemProps = {
  item: CollectionItem;
  collectionName?: string;
  indexed?: boolean;
  expandable?: boolean;
  rank?: number;
  className?: string;
} & HTMLAttributes<HTMLAnchorElement>;

export default function CompactBondCard({
  item,
  rank = 0,
  indexed = false,
  expandable = false,
  className,
  collectionName = "",
  ...rest
}: CollectionItemProps) {
  const handleBondsCollectionItemClicked = () => {
    trackEvent("bonds_collection_item_clicked", {
      bond_name: item.aboutTheInstitution?.bondInstitutionName,
      bond_type: InvestabilityStatus[item.investabilityStatus],
      bond_id: item.id,
      collectionName,
      rank,
    });
  };
  return expandable ? (
    <Surface elevation="md">
      <div className="relative flex items-center gap-5 px-9 py-6">
        <EntityLogo
          size="large"
          url={item.aboutTheInstitution?.logo || ""}
          color={item.bgColor}
          greyBorder
          elevation="none"
        />
        <div className="space-y-1">
          <div>
            <p className="relative space-x-[4px] bg-white text-left md:space-x-[6.8px]">
              <span className="text-black-80 text-heading3 font-medium">
                {formatAmount(item.xirr)}%
              </span>
              {item.struckenYield > 0 && (
                <span className="relative inline-block">
                  <span className="text-black-40 text-body2 font-medium">
                    {formatAmount(item.struckenYield)}%
                  </span>
                  <img
                    src={redStrike}
                    alt="red-strike"
                    className="absolute top-3 left-0 z-20 w-6 md:w-11"
                  />
                </span>
              )}
              <span className="text-black-50 text-body2">YTM</span>
            </p>
          </div>
          <div className="flex items-center gap-[6px] md:gap-3">
            <p className="text-black-80 text-body1 flex-shrink-0">
              {item.tenure}
            </p>
            <div className="bg-black-40 h-1 w-1 flex-shrink-0 rounded-full md:h-2 md:w-2"></div>
            <p className="text-black-40 text-body1 truncate">
              {item.displayTitle}
            </p>
            <img
              src={chevron}
              alt="chevron-right"
              className="w-[5px] md:w-[7px] md:pt-[1px]"
            />
          </div>
        </div>
        {indexed && rank && (
          <GradientNumber
            value={rank}
            className="absolute right-3 -bottom-11 text-[104px]"
          />
        )}
      </div>
    </Surface>
  ) : (
    <Anchor
      href={makeBondUrl(item)}
      className={className}
      onClick={handleBondsCollectionItemClicked}
      {...rest}
    >
      <Surface elevation="md">
        <div className="relative flex items-center gap-5 px-6 py-6">
          <EntityLogo
            size="large"
            url={item.aboutTheInstitution?.logo || ""}
            color={item.bgColor}
            greyBorder
            elevation="none"
          />
          <div className="space-y-1">
            <div>
              <p className="relative space-x-[4px] bg-white text-left md:space-x-[6.8px]">
                <span className="text-black-80 text-heading3 font-medium">
                  {formatAmount(item.xirr)}%
                </span>
                {item.struckenYield > 0 && (
                  <span className="relative inline-block">
                    <span className="text-black-40 text-body2 font-medium">
                      {formatAmount(item.struckenYield)}%
                    </span>
                    <img
                      src={redStrike}
                      alt="red-strike"
                      className="absolute top-3 left-0 z-20 w-6 md:w-11"
                    />
                  </span>
                )}
                <span className="text-black-50 text-body2">YTM</span>
              </p>
            </div>
            <div className="flex items-center gap-[6px] md:gap-3">
              <p className="text-black-80 text-body1 flex-shrink-0">
                {item.tenure}
              </p>
              <div className="bg-black-40 h-1 w-1 flex-shrink-0 rounded-full md:h-2 md:w-2"></div>
              <p className="text-black-40 text-body1 truncate">
                {item.displayTitle}
              </p>
              <img
                src={chevron}
                alt="chevron-right"
                className="w-[5px] md:w-[7px] md:pt-[1px]"
              />
            </div>
          </div>
          {indexed && rank && (
            <GradientNumber
              value={rank}
              className="absolute right-3 -bottom-11 text-[104px]"
            />
          )}
        </div>
      </Surface>
    </Anchor>
  );
}
