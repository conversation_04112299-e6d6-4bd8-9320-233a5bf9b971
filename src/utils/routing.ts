import type { BondDetailItem } from "@/clients/gen/broking/BondDetails_pb";
import type { CollectionItem } from "@/clients/gen/broking/Collection_pb";

export function rewritePath(path: string) {
  if (path.startsWith("/onboarding")) {
    return "/workflow/onboarding-initiation";
  }

  if (path.startsWith("/bonds-home")) {
    return "/app";
  }

  // Redirect /app/bonds/{uuid} to /bonds/unknown/{uuid}
  if (
    path.startsWith("/app/bonds/") &&
    (path.length === 47 || path.length === 23)
  ) {
    const bondId = path.split("/")[3];
    return `/bonds/unknown/${bondId}`;
  }

  // Redirect /bonds/{uuid} to /bonds/unknown/{uuid}
  if (
    path.startsWith("/bonds/") &&
    !path.startsWith("/bonds/unknown/") &&
    (path.length === 43 || path.length === 19)
  ) {
    const bondId = path.split("/")[2];
    return `/bonds/unknown/${bondId}`;
  }

  // Redirect /bond_details/{id} to /bonds/unknown/{id}
  if (path.match(/\/bond_details\/[a-zA-Z0-9-]+/)) {
    const bondId = path.split("/")[2];
    return `/bonds/unknown/${bondId}`;
  }

  // Redirect /app{anything} to /app/{anything} (add slash after app)
  if (path.match(/^\/app[^/]/)) {
    return path.replace(/^\/app/, "/");
  }

  // Redirect /checkout/orders/{uuid} to /checkout/{uuid}
  if (path.startsWith("/checkout/orders/")) {
    const chunks = path.split("/");
    const orderId = chunks[3];
    return `/checkout/${orderId}`;
  }

  // Redirect payment status pages to checkout orders
  if (path.startsWith("/payments/status")) {
    const chunks = path.split("/");
    const orderId = chunks[3];
    return `/checkout/${orderId}`;
  }

  if (path.startsWith("/bonds/payment/status")) {
    const chunks = path.split("/");
    const orderId = chunks[4];
    return `/checkout/${orderId}`;
  }

  // Redirect onboarding status pages
  if (
    path.startsWith("/bonds_status_page") ||
    path.startsWith("/bond_onboarding_analyze")
  ) {
    return "/workflow/onboarding-initiation";
  }

  // Redirect personalization pages
  if (path.startsWith("/generic/") || path.startsWith("/page/")) {
    const slug = path.split("/")[2];
    return `/dynamic/${slug}`;
  }

  if (path.startsWith("/app/page/")) {
    const slug = path.split("/")[3];
    return `/dynamic/${slug}`;
  }

  return path;
}

export function makeBondUrl(item: CollectionItem | BondDetailItem) {
  return `/bonds/${item.aboutTheInstitution?.slug ?? "unknown"}/${item.isinCode}`;
}

export function isHotwireNative() {
  return window.navigator.userAgent.includes("Hotwire Native");
}

export function isFlutterWebView() {
  return !!window.flutter_inappwebview;
}

// Returns /app for inside app and /home for browser
export function getHomeUrl() {
  return isHotwireNative() || isFlutterWebView() ? "/app" : "/home";
}

export function getInvestmentsUrl() {
  return isHotwireNative()
    ? "/flutter/passbook_page?initialIndex=1&presentation=REPLACE"
    : "/investments";
}

export function goBack() {
  if (isHotwireNative()) {
    return window.HotwireNavigator.visitProposedToLocation(
      new URL("/turbo_recede_historical_location_url", window.location.origin)
    );
  }
  if (window.flutter_inappwebview) {
    const currentUrl = window.location.href;
    history.back();
    setTimeout(() => {
      if (window.location.href === currentUrl) {
        // The undefined values are required, without which the flutter navigation fails.
        return window.flutter_inappwebview?.callHandler(
          "navigate",
          "pop",
          undefined,
          undefined
        );
      }
    }, 100);
  }
  history.back();
}

export function navigate(path: string, e: Event) {
  if (window.flutter_inappwebview) {
    e.preventDefault();
    return window.flutter_inappwebview?.callHandler(
      "navigate",
      "push",
      path,
      "HOTWIRE"
    );
  }
}
