@import "tailwindcss" source("../");
@plugin '@tailwindcss/typography';
@plugin 'tailwind-scrollbar';
@plugin 'tailwindcss-safe-area';

@font-face {
  font-family: "CircularStd";
  font-weight: 400;
  src: url("https://assets.stablemoney.in/fonts/CircularStd-Book.woff")
    format("woff");
  font-display: swap;
}

@font-face {
  font-family: "CircularStd";
  font-weight: 400;
  font-style: italic;
  src: url("https://assets.stablemoney.in/fonts/CircularStd-BookItalic.woff")
    format("woff");
  font-display: swap;
}

@font-face {
  font-family: "CircularStd";
  font-weight: 500;
  src: url("https://assets.stablemoney.in/fonts/CircularStd-Medium.woff")
    format("woff");
  font-display: swap;
}

@font-face {
  font-family: "CircularStd";
  font-weight: 500;
  font-style: italic;
  src: url("https://assets.stablemoney.in/fonts/CircularStd-MediumItalic.woff")
    format("woff");
  font-display: swap;
}

@font-face {
  font-family: "RecklessNeue";
  font-weight: 400;
  src: url("https://assets.stablemoney.in/fonts/RecklessNeue-Book.woff2")
    format("woff2");
  font-display: swap;
}

@font-face {
  font-family: "RecklessNeue";
  font-weight: 400;
  font-style: italic;
  src: url("https://assets.stablemoney.in/fonts/RecklessNeue-BookItalic.woff2")
    format("woff2");
  font-display: swap;
}

@font-face {
  font-family: "RecklessNeue";
  font-weight: 500;
  src: url("https://assets.stablemoney.in/fonts/RecklessNeue-Medium.woff2")
    format("woff2");
  font-display: swap;
}

@font-face {
  font-family: "RecklessNeue";
  font-weight: 500;
  font-style: italic;
  src: url("https://assets.stablemoney.in/fonts/RecklessNeue-MediumItalic.woff2")
    format("woff2");
  font-display: swap;
}

@theme {
  /* Reset */
  --color-*: initial;
  --radius-*: initial;
  --shadow-*: initial;

  /* Colors */
  --color-black-80: rgba(0, 0, 0, 0.8);
  --color-black-60: rgba(0, 0, 0, 0.6);
  --color-black-50: rgba(0, 0, 0, 0.5);
  --color-black-40: rgba(0, 0, 0, 0.4);
  --color-black-20: rgba(0, 0, 0, 0.2);
  --color-black-10: rgba(0, 0, 0, 0.1);
  --color-black-5: rgba(0, 0, 0, 0.05);
  --color-black-3: rgba(0, 0, 0, 0.03);
  --color-white: white;
  --color-black: black;
  --color-purple: #916cff;
  --color-green: #12be57;
  --color-neon-green: #c5ff18;
  --color-yellow: #ffb003;
  --color-bg: #fcfcfc;
  --color-red: #ee4950;
  --color-orange: #f47126;
  --color-border: var(--color-black-10);

  /* Typography */
  --text-*: initial;
  --font-sans: CircularStd, sans-serif;
  --font-serif: RecklessNeue, serif;
  --font-mono:
    ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono",
    "Courier New", monospace;

  --text-heading1: calc(22px * var(--sd-scale-factor, 1));
  --text-heading1--line-height: 1.45;
  --text-heading1--letter-spacing: calc(-0.5px * var(--sd-scale-factor, 1));

  --text-heading2: calc(20px * var(--sd-scale-factor, 1));
  --text-heading2--line-height: 1.4;
  --text-heading2--letter-spacing: calc(-0.5px * var(--sd-scale-factor, 1));

  --text-heading3: calc(18px * var(--sd-scale-factor, 1));
  --text-heading3--line-height: 1.44;
  --text-heading3--letter-spacing: calc(-0.4px * var(--sd-scale-factor, 1));

  --text-heading4: calc(15px * var(--sd-scale-factor, 1));
  --text-heading4--line-height: 1.46;
  --text-heading4--letter-spacing: calc(-0.2px * var(--sd-scale-factor, 1));

  --text-body1: calc(13px * var(--sd-scale-factor, 1));
  --text-body1--line-height: 1.53;
  --text-body1--letter-spacing: calc(-0.2px * var(--sd-scale-factor, 1));

  --text-body2: calc(11px * var(--sd-scale-factor, 1));
  --text-body2--line-height: 1.36;
  --text-body2--letter-spacing: 0;

  --text-caption: calc(9px * var(--sd-scale-factor, 1));
  --text-caption--line-height: 1.22;
  --text-caption--letter-spacing: 0;

  --text-title-all-caps: calc(11px * var(--sd-scale-factor, 1));
  --text-title-all-caps--line-height: 1.36;
  --text-title-all-caps--letter-spacing: 1.5px;

  /* Sizes */
  --spacing: calc(4px * var(--sd-scale-factor, 1));

  /* Borders */
  --radius-xl: calc(12px * var(--sd-scale-factor, 1));
  --radius-lg: calc(10px * var(--sd-scale-factor, 1));
  --radius-sm: calc(8px * var(--sd-scale-factor, 1));
  --radius-xs: calc(4px * var(--sd-scale-factor, 1));
  --border-w-xs: calc(0.3px * var(--sd-scale-factor, 1));
  --border-w-sm: calc(0.5px * var(--sd-scale-factor, 1));
  --border-w-md: calc(1px * var(--sd-scale-factor, 1));
  --default-border-width: var(--border-w-sm);

  --shadow-md: 0 0 25px var(--color-black-5);
  --drop-shadow-md: 0 0 25px var(--color-black-5);
}

:root {
  --sd-scale-factor: 1.14;
  --sat: env(safe-area-inset-top);
  --app-bar-height: 75 + var(--sat);

  @media screen and (min-width: 768px) {
    --sd-scale-factor: 1;
    --app-bar-height: 0;

    --text-heading1: 24px;
    --text-heading1--line-height: 1.5;
    --text-heading1--letter-spacing: -0.4px;

    --text-heading2: 22px;
    --text-heading2--line-height: 1.45;
    --text-heading2--letter-spacing: -0.5px;

    --text-heading3: 20px;
    --text-heading3--line-height: 1.4;
    --text-heading3--letter-spacing: -0.5px;

    --text-heading4: 18px;
    --text-heading4--line-height: 1.44;
    --text-heading4--letter-spacing: -0.2px;

    --text-body1: 15px;
    --text-body1--line-height: 1.46;
    --text-body1--letter-spacing: -0.2px;

    --text-body2: 13px;
    --text-body2--line-height: 1.53;
    --text-body2--letter-spacing: -0.2px;

    --text-caption: 11px;
    --text-caption--line-height: 1.36;

    --text-title-all-caps: 13px;
    --text-title-all-caps--line-height: 1.53;
  }

  @apply text-black-80 bg-bg font-sans;
}

@layer utilities {
  * {
    @apply border-black-10;
  }

  header:has(+ .body-behind-appbar) {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
  }

  .android.hotwire-native .pt-safe,
  .android.flutter .pt-safe {
    padding-top: max(env(safe-area-inset-top), 40px);
  }

  .hotwire-native,
  .flutter {
    user-select: none;
  }

  hr {
    border-top-width: calc(var(--sd-scale-factor) * 1.5px);
  }

  .text-title-all-caps {
    text-transform: uppercase;
  }

  .floating-footer-margin {
    margin-bottom: var(--floating-footer-height);
  }

  .floating-footer-padding {
    padding-bottom: var(--floating-footer-height);
  }

  .app-bar-height-top-padding {
    padding-top: var(--app-bar-height);
  }
}

body {
  padding: 0.1px;
}

/* Unit Selector - Hide number input spinners */
.unit-selector-input::-webkit-inner-spin-button,
.unit-selector-input::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

button {
  outline: none;
}

button:focus {
  outline: none;
}

/* Fix for iOS Chrome double tap issue */
@media (hover: none) and (pointer: coarse) {
  /* Remove cursor pointer on touch devices to prevent hover state */
  .cursor-pointer {
    cursor: default !important;
  }

  /* Ensure touch actions are optimized for touch devices */
  a,
  button,
  [role="button"],
  [onclick] {
    touch-action: manipulation;
    -webkit-touch-callout: none;
  }
}

#bonds-home header [name="back"] {
  display: none;
}

.hotwire-native #bonds-home header [name="back"],
.flutter #bonds-home header [name="back"] {
  display: block;
}
