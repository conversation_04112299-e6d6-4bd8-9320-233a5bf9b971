import {
  type FilterItem,
  type FilterOptionValue,
} from "@/clients/gen/broking/FilterSearch_pb";
import type { AppliedFilter, FilterOptionValueWithLabel } from "../../machine";
import Chip from "@/components/ui/chip/chip";

interface ProgressiveFilterRendererProps {
  item: FilterItem;
  handleFilter: (
    filters: FilterOptionValue,
    isSelected: boolean,
    selectedIndex: number
  ) => void;
  appliedFilters: AppliedFilter[];
}

const ProgressiveFilterRenderer = ({
  item,
  handleFilter,
  appliedFilters,
}: ProgressiveFilterRendererProps) => {
  const renderFilterContent = () => {
    return (
      <div className="mt-3 flex flex-wrap gap-2">
        {item.options.map((option, index) => (
          <Chip
            key={`${item.key}-${index}`}
            selected={appliedFilters.some(
              (filter) =>
                filter.key === item.key &&
                filter.filters.includes(
                  option.optionValue as FilterOptionValueWithLabel
                )
            )}
            size="medium"
            variant="outlined"
            onClick={() => {
              handleFilter(option.optionValue!, !option.isSelected, index);
            }}
            className="text-body1 border-1 bg-white"
          >
            {option.label}
          </Chip>
        ))}
      </div>
    );
  };

  return (
    <div className="space-y-2">
      <h4 className="text-heading4 text-black-80">{item.label}</h4>
      {renderFilterContent()}
    </div>
  );
};

export default ProgressiveFilterRenderer;
