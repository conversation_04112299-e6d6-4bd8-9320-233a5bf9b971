import type {
  SortConfig,
  SortType,
} from "@/clients/gen/broking/FilterSearch_pb";
import { RadioGroup, RadioItem } from "@/components/ui/radio-group";

interface SortTrayProps {
  sortConfig?: SortConfig;
  handleSort: (sortType: SortType) => void;
  currentSort: SortType;
  toggleSortPanel: () => void;
}

const SortTray = ({
  sortConfig,
  handleSort,
  currentSort,
  toggleSortPanel,
}: SortTrayProps) => {
  const handleSortChange = (value: string) => {
    const sortType = parseInt(value) as SortType;
    handleSort(sortType);
    toggleSortPanel();
  };

  return (
    <div className="flex flex-col space-y-4 px-5 pb-8">
      <p className="text-heading4 font-medium">{sortConfig?.title}</p>
      <RadioGroup
        name={sortConfig?.title ?? ""}
        onChange={handleSortChange}
        className="space-y-4"
        value={currentSort.toString()}
      >
        {sortConfig?.items.map((item) => (
          <div key={item.type} className="space-y-4">
            <hr className="border-black-10 border-t" />
            <RadioItem
              className="flex-row-reverse justify-between"
              value={item.type.toString()}
              isChecked={item.type === currentSort}
            >
              {item.label}
            </RadioItem>
          </div>
        ))}
      </RadioGroup>
    </div>
  );
};

export default SortTray;
