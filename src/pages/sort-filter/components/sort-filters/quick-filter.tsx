import {
  FilterType,
  type FilterItem,
  type FilterOptionValue,
} from "@/clients/gen/broking/FilterSearch_pb";
import Switch from "@/components/ui/switch/switch";
import type { AppliedFilter } from "../../machine";
import { t } from "i18next";
import CheckBoxDropdown from "@/components/ui/checkbox-dropdown";
import "../bonds-listing/index.css";

const CheckBoxDropdownShimmer = () => {
  return (
    <div className="border-black-10 flex items-center justify-between gap-1 rounded-sm border px-[10px] py-[6px]">
      <div className="shimmer h-4 w-16 rounded"></div>
      <div className="shimmer h-[7px] w-[7px] rounded"></div>
    </div>
  );
};

interface QuickFilterProps {
  quickFilter?: FilterItem[];
  handleFilterChange: (
    filter: {
      key: string;
      filters: FilterOptionValue;
      label: string;
    },
    isSelected: boolean
  ) => void;
  appliedFilters: AppliedFilter[];
  isLoading: boolean;
}

interface ItemRendererProps {
  item?: FilterItem;
  handleFilterChange: (
    filters: FilterOptionValue,
    isSelected: boolean,
    label: string
  ) => void;
  appliedFilters: AppliedFilter[];
  isLoading?: boolean;
}
const ItemRenderer = ({
  item,
  handleFilterChange,
  appliedFilters,
  isLoading = false,
}: ItemRendererProps) => {
  if (isLoading || !item) {
    return <CheckBoxDropdownShimmer />;
  }

  switch (item.type) {
    case FilterType.MULTI_SELECT_PILLS:
    case FilterType.RANGE_MULTI_SELECT:
    case FilterType.MULTI_SELECT:
    case FilterType.MULTI_SELECT_WITH_ICON:
      return (
        <CheckBoxDropdown
          options={item.options.map((option) => ({
            label: option.label,
            value: option.label,
          }))}
          placeholder={t(item.shortLabel)}
          onChange={(index, isSelected) => {
            handleFilterChange(
              item.options[index].optionValue!,
              isSelected,
              item.options[index].label
            );
          }}
          appliedFilters={appliedFilters}
        />
      );

    case FilterType.BOOLEAN_SELECT:
      return (
        <div className="border-black-10 flex items-center gap-2 rounded-sm border px-[10px] py-[6px]">
          {t(item.shortLabel)}
          <Switch
            name={item.key}
            defaultChecked={item.options[0]?.isSelected || false}
            density="compact"
          />
        </div>
      );
    default:
      return null;
  }
};

const QuickFilter = ({
  quickFilter,
  handleFilterChange,
  appliedFilters,
  isLoading,
}: QuickFilterProps) => {
  return (
    <div className="quick-filter scrollbar-none flex gap-3 overflow-x-scroll">
      {isLoading
        ? Array.from({ length: 4 }).map((_, index) => (
            <div
              key={`shimmer-${index}`}
              className="flex w-fit shrink-0 flex-col"
            >
              <ItemRenderer
                isLoading={true}
                handleFilterChange={() => {}}
                appliedFilters={[]}
              />
            </div>
          ))
        : quickFilter?.map((item, index) => {
            return (
              <div key={index} className="flex w-fit shrink-0 flex-col">
                <ItemRenderer
                  item={item}
                  handleFilterChange={(filters, isSelected, label) => {
                    handleFilterChange(
                      {
                        key: item.key,
                        filters,
                        label: label,
                      },
                      isSelected
                    );
                  }}
                  appliedFilters={appliedFilters}
                />
              </div>
            );
          })}
    </div>
  );
};

export default QuickFilter;
