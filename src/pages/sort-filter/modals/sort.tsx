import AdaptiveModal from "@/components/ui/adaptive-modal/adaptive-modal";
import Button from "@/components/ui/button/button";
import smsSortIcon from "@/assets/images/icons/sms_sort_icon.svg";
import SortTray from "../components/sort-filters/sort-tray";
import type {
  FilterSortConfigResponse,
  SortType,
} from "@/clients/gen/broking/FilterSearch_pb";

interface SortButtonProps {
  filterConfig?: FilterSortConfigResponse;
  handleSort: (sortType: SortType) => void;
  currentSort: SortType;
  isSortChanged: boolean;
}

const SortButton = ({
  isSortChanged,
  handleSort,
  filterConfig,
  currentSort,
}: SortButtonProps) => {
  return (
    <AdaptiveModal
      trigger={
        <Button className="h-10! shrink-1 rounded-lg!">
          <span className="flex items-center gap-1">
            <img src={smsSortIcon} alt="sort" />
            <p className="text-title-all-caps">SORT BY</p>
            {isSortChanged && (
              <span className="bg-neon-green h-1 w-1 rounded-[1px]"></span>
            )}
          </span>
        </Button>
      }
      size="small"
    >
      {({ dismiss }) => (
        <SortTray
          sortConfig={filterConfig?.sort}
          handleSort={(sortType) => {
            handleSort(sortType);
          }}
          currentSort={currentSort}
          toggleSortPanel={dismiss}
        />
      )}
    </AdaptiveModal>
  );
};

export default SortButton;
