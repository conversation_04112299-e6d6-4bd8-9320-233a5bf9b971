import AdaptiveModal from "@/components/ui/adaptive-modal/adaptive-modal";
import Button from "@/components/ui/button/button";
import FilterTray from "../components/sort-filters/filter-tray";
import smsFilterIcon from "@/assets/images/icons/sms_filter_icon.svg";
import type {
  FilterOptionValue,
  FilterSortConfigResponse,
} from "@/clients/gen/broking/FilterSearch_pb";
import type { AppliedFilter } from "../machine";

interface FilterButtonProps {
  filterConfig?: FilterSortConfigResponse;
  handleFilterChange: (
    filter: {
      key: string;
      filters: FilterOptionValue;
      label: string;
    },
    isSelected: boolean,
    selectedIndex: number
  ) => void;
  appliedFilters: AppliedFilter[];
  handleClearFilters: () => void;
  isLoadingResults: boolean;
  currentPage: number;
  totalResults: number;
}

const FilterButton = ({
  appliedFilters,
  handleFilterChange,
  handleClearFilters,
  totalResults,
  filterConfig,
  currentPage,
  isLoadingResults,
}: FilterButtonProps) => {
  return (
    <AdaptiveModal
      trigger={
        <Button className="h-10! shrink-1 rounded-lg!">
          <span className="flex items-center gap-1">
            <img src={smsFilterIcon} alt="filter" />
            <p className="text-title-all-caps">FILTERS </p>
            {appliedFilters.length > 0 && (
              <span className="bg-neon-green h-1 w-1 rounded-[1px]"></span>
            )}
          </span>
        </Button>
      }
      size="small"
      href="/sheets/mandatory-documents"
    >
      {({ dismiss }) => (
        <FilterTray
          filterConfig={filterConfig?.filter}
          totalResults={totalResults}
          toggleFilterPanel={dismiss}
          appliedFilters={appliedFilters}
          handleFilterChange={handleFilterChange}
          handleClearFilters={handleClearFilters}
          isLoading={isLoadingResults && currentPage === 0}
        />
      )}
    </AdaptiveModal>
  );
};

export default FilterButton;
