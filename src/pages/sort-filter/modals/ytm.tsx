import AdaptiveModal from "@/components/ui/adaptive-modal/adaptive-modal";
import infoIcon from "@/assets/images/icons/info.svg";

const YtmModal = () => {
  return (
    <AdaptiveModal trigger={<img src={infoIcon} />} size="small">
      {() => (
        <div className="flex flex-col gap-5 p-5 pb-7">
          <p className="text-heading4 text-black-80 font-medium">
            Yield to maturity
          </p>
          <p className="text-heading4 text-black-50">
            Yield to Maturity (YTM) is the estimated return you'll earn if you
            hold the bond until it matures, assuming you buy it at today's price
            and receive all interest payments on time.
          </p>
          <p>
            It helps you compare different bonds by showing the effective annual
            return, including both interest income and any gain or loss if the
            bond is bought at a discount or premium
          </p>
        </div>
      )}
    </AdaptiveModal>
  );
};

export default YtmModal;
