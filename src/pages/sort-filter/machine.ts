import { setup, assign, fromPromise } from "xstate";
import { getConfig, getFilterResults } from "@/queries/filter-sorts";
import { create } from "@bufbuild/protobuf";
import {
  FilterSortQueryRequestSchema,
  SortType,
  type FilterSortConfigResponse,
  type BondItem,
  type FilterItem,
  type FilterQuery,
  type FilterOptionValue,
  RangeValueSchema,
  FilterOptionValueSchema,
  FilterQuerySchema,
  type EmptyState,
} from "@/clients/gen/broking/FilterSearch_pb";
import type { PaginationRequest } from "@/clients/gen/broking/Common_pb";
import { PaginationRequestSchema } from "@/clients/gen/broking/Common_pb";

export type FilterOptionValueWithLabel = FilterOptionValue & {
  label?: string;
  value: string | boolean;
};

export type AppliedFilter = {
  key: string;
  filters: FilterOptionValueWithLabel[];
  label: string;
};

type FilterSortContext = {
  filterConfig?: FilterSortConfigResponse;
  appliedFilters: AppliedFilter[];
  currentSort: SortType;

  // Results from backend
  bonds: BondItem[];
  totalResults: number;
  suggestedFilters: { [key: number]: FilterItem };
  emptyState?: EmptyState;

  // Pagination
  currentPage: number;
  itemsPerPage: number;
  totalPages: number;

  // Error handling
  error?: unknown;

  // Loading states
  isLoadingConfig: boolean;
  isLoadingResults: boolean;
};

function buildFilterQueries(appliedFilters: AppliedFilter[]): FilterQuery[] {
  return appliedFilters.map((filter) => {
    const processedOptions = filter.filters.map((filterOption) => {
      if (filterOption.optionValue?.case) {
        return filterOption;
      }

      if (filterOption) {
        if ("lowerBound" in filterOption && "upperBound" in filterOption) {
          return create(FilterOptionValueSchema, {
            optionValue: {
              case: "rangeValue",
              value: create(RangeValueSchema, {
                lowerBound: filterOption.lowerBound as number,
                upperBound: filterOption.upperBound as number,
              }),
            },
          });
        }

        if (
          "boolValue" in filterOption ||
          typeof filterOption.value === "boolean"
        ) {
          const boolValue = (
            "boolValue" in filterOption
              ? filterOption.boolValue
              : typeof filterOption.value === "boolean"
          ) as boolean;
          return create(FilterOptionValueSchema, {
            optionValue: {
              case: "boolValue",
              value: boolValue,
            },
          });
        }

        if (
          "stringValue" in filterOption ||
          typeof filterOption.value === "string"
        ) {
          const stringValue = (
            "stringValue" in filterOption
              ? filterOption.stringValue
              : filterOption.value
          ) as string;
          return create(FilterOptionValueSchema, {
            optionValue: {
              case: "stringValue",
              value: stringValue,
            },
          });
        }
      }

      return filterOption;
    });

    return create(FilterQuerySchema, {
      key: filter.key,
      options: processedOptions,
    });
  });
}

function buildPaginationRequest(
  page: number,
  itemsPerPage: number
): PaginationRequest {
  return create(PaginationRequestSchema, {
    page: page,
    size: itemsPerPage,
  });
}

const hasResults = ({ context }: { context: FilterSortContext }) => {
  return context.bonds.length > 0;
};

const hasFiltersApplied = ({ context }: { context: FilterSortContext }) => {
  return context.appliedFilters.length > 0;
};

export const filterSortMachine = setup({
  types: {
    context: {} as FilterSortContext,
    output: {} as {
      bonds: BondItem[];
      totalResults: number;
      currentPage: number;
      totalPages: number;
      filterConfig?: FilterSortConfigResponse;
    },
  },
  actors: {
    loadConfig: fromPromise(async () => {
      return await getConfig();
    }),

    loadFilterResults: fromPromise(
      async ({
        input,
      }: {
        input: {
          filters: AppliedFilter[];
          sort: SortType;
          page: number;
          itemsPerPage: number;
        };
      }) => {
        const queryRequest = create(FilterSortQueryRequestSchema, {
          filters: buildFilterQueries(input.filters),
          sort: input.sort,
          paginationRequest: buildPaginationRequest(
            input.page,
            input.itemsPerPage
          ),
        });
        return await getFilterResults(queryRequest);
      }
    ),
  },
  guards: {
    hasResults,
    hasFiltersApplied,
  },
}).createMachine({
  id: "filterSort",
  initial: "loadingConfig",
  context: {
    // Configuration from backend
    filterConfig: undefined,

    // Current filter/sort state
    appliedFilters: [],
    currentSort: SortType.YTM_HIGH_TO_LOW,

    // Results from backend
    bonds: [],
    totalResults: 0,
    suggestedFilters: {},
    emptyState: undefined,

    // Pagination
    currentPage: 0,
    itemsPerPage: 20,
    totalPages: 0,

    // Error handling
    error: undefined,

    // Loading states
    isLoadingConfig: false,
    isLoadingResults: false,
  },
  states: {
    loadingConfig: {
      entry: assign({
        isLoadingConfig: () => true,
        error: () => undefined,
      }),
      invoke: {
        src: "loadConfig",
        onDone: {
          target: "loadingResults",
          actions: assign({
            filterConfig: ({ event }) => event.output,
            isLoadingConfig: () => false,
          }),
        },
        onError: {
          target: "configError",
          actions: assign({
            error: ({ event }) => event.error,
            isLoadingConfig: () => false,
          }),
        },
      },
    },

    configError: {
      on: {
        RETRY_LOAD_CONFIG: "loadingConfig",
      },
    },

    resultsError: {
      on: {
        RETRY_LOAD_RESULTS: "loadingResults",
        APPLY_FILTERS: "loadingResults",
        UPDATE_SORT: "loadingResults",
      },
    },

    loadingResults: {
      entry: assign({
        isLoadingResults: () => true,
      }),
      invoke: {
        src: "loadFilterResults",
        input: ({ context }) => ({
          filters: context.appliedFilters,
          sort: context.currentSort,
          page: context.currentPage,
          itemsPerPage: context.itemsPerPage,
          filterConfig: context.filterConfig,
        }),
        onDone: {
          target: "idle",
          actions: assign({
            bonds: ({ event, context }) => {
              // For page 1 or when filters/sort change, replace the array
              // For subsequent pages, append to existing array
              if (context.currentPage === 0) {
                return event.output.items || [];
              } else {
                return [...context.bonds, ...(event.output.items || [])];
              }
            },
            totalResults: ({ event }) => event.output.count || 0,
            totalPages: ({ context, event }) =>
              Math.ceil((event.output.count || 0) / context.itemsPerPage),
            suggestedFilters: ({ event }) =>
              event.output.suggestedFilters || {},
            emptyState: ({ event }) => event.output.emptyState,
            isLoadingResults: () => false,
          }),
        },
        onError: {
          target: "resultsError",
          actions: assign({
            error: ({ event }) => event.error,
            isLoadingResults: () => false,
          }),
        },
      },
    },

    idle: {
      always: [
        {
          target: "hasResults",
          guard: "hasResults",
        },
        {
          target: "noResults",
        },
      ],
      on: {
        RELOAD_CONFIG: "loadingConfig",
        APPLY_FILTERS: {
          target: "loadingResults",
          actions: assign({
            appliedFilters: ({ event }) => event.filters,
            currentPage: () => 0,
            bonds: () => [],
          }),
        },
        CLEAR_FILTERS: {
          target: "loadingResults",
          actions: assign({
            appliedFilters: () => [],
            currentPage: () => 0,
            bonds: () => [],
          }),
        },
        UPDATE_SORT: {
          target: "loadingResults",
          actions: assign({
            currentSort: ({ event }) => event.sortType,
            currentPage: () => 0,
            bonds: () => [],
          }),
        },
        SET_PAGE: {
          target: "loadingResults",
          actions: assign({
            currentPage: ({ event, context }) =>
              Math.min(Math.max(0, event.page), context.totalPages),
          }),
        },
        SET_ITEMS_PER_PAGE: {
          target: "loadingResults",
          actions: assign({
            itemsPerPage: ({ event }) => event.itemsPerPage,
            currentPage: () => 0,
          }),
        },
      },
    },

    hasResults: {
      on: {
        RELOAD_CONFIG: "loadingConfig",
        APPLY_FILTERS: {
          target: "loadingResults",
          actions: assign({
            appliedFilters: ({ event }) => event.filters,
            currentPage: () => 0,
            bonds: () => [],
          }),
        },
        CLEAR_FILTERS: {
          target: "loadingResults",
          actions: assign({
            appliedFilters: () => [],
            currentPage: () => 0,
            bonds: () => [],
          }),
        },
        UPDATE_SORT: {
          target: "loadingResults",
          actions: assign({
            currentSort: ({ event }) => event.sortType,
            currentPage: () => 0,
            bonds: () => [],
          }),
        },
        SET_PAGE: {
          target: "loadingResults",
          actions: assign({
            currentPage: ({ event, context }) =>
              Math.min(Math.max(0, event.page), context.totalPages),
          }),
        },
        SET_ITEMS_PER_PAGE: {
          target: "loadingResults",
          actions: assign({
            itemsPerPage: ({ event }) => event.itemsPerPage,
            currentPage: () => 0,
          }),
        },
      },

      always: [
        {
          target: "noResults",
          guard: ({ context }) => context.bonds.length === 0,
        },
      ],
    },

    noResults: {
      on: {
        RELOAD_CONFIG: "loadingConfig",
        APPLY_FILTERS: {
          target: "loadingResults",
          actions: assign({
            appliedFilters: ({ event }) => event.filters,
            currentPage: () => 0,
            bonds: () => [],
          }),
        },
        CLEAR_FILTERS: {
          target: "loadingResults",
          actions: assign({
            appliedFilters: () => [],
            currentPage: () => 0,
            bonds: () => [],
          }),
        },
        UPDATE_SORT: {
          target: "loadingResults",
          actions: assign({
            currentSort: ({ event }) => event.sortType,
            currentPage: () => 0,
            bonds: () => [],
          }),
        },
      },

      always: [
        {
          target: "hasResults",
          guard: "hasResults",
        },
      ],
    },
  },
});
