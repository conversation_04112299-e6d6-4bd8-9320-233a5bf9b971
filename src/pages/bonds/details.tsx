import { buildPersonalizationWidget } from "@/components/personalization/builder";
import Surface from "@/components/ui/surface/surface";
import {
  personalizationPageQuery,
  createBondDetailsQueryOptions,
} from "@/queries/bonds";
import { useMediaQuery } from "@react-hook/media-query";
import { useSuspenseQuery } from "@tanstack/react-query";
import { useParams, type LoaderFunctionArgs } from "react-router";
import BondCalculator from "@/components/bonds/bond-calculator";
import SecondaryHeader from "@/components/layouts/secondary-header";
import Footer from "@/pages/landing-page/components/footer";
import Head from "@/components/ui/head";
import { queryClient } from "@/queries/client";

export async function loader({ params }: LoaderFunctionArgs) {
  const bondId = params.bond_id;
  if (!bondId) throw new Error("Bond ID is required");

  await Promise.all([
    queryClient.ensureQueryData(
      personalizationPageQuery("bond-details", {
        bond_id: bondId,
      })
    ),
    queryClient.ensureQueryData(createBondDetailsQueryOptions(bondId)),
    queryClient.removeQueries({
      queryKey: [`${bondId}-calculation-value`],
    }),
  ]);
}

function DesktopPage() {
  const params = useParams();
  const { data: pageResponse } = useSuspenseQuery(
    personalizationPageQuery("bond-details", {
      bond_id: params.bond_id,
    })
  );
  const { data: bondDetails } = useSuspenseQuery(
    createBondDetailsQueryOptions(params.bond_id!)
  );

  return (
    <>
      <SecondaryHeader hideHeader />
      <Head
        title={`${bondDetails.aboutTheInstitution?.title} - Stable Bonds`}
      />
      <div className="container mx-auto contents md:m-auto md:my-16 md:flex md:justify-evenly md:gap-8">
        <Surface
          borderWidth="md"
          elevation="md"
          className="max-w-[734px] flex-grow-1"
        >
          {buildPersonalizationWidget(pageResponse.root)}
        </Surface>
        <aside className="sticky top-18 hidden h-fit md:block md:min-w-[300px] lg:min-w-[350px]">
          <BondCalculator details={bondDetails} />
        </aside>
      </div>
      <div className="hidden md:block">
        <Footer />
      </div>
    </>
  );
}

function MobilePage() {
  const params = useParams();
  const { data: pageResponse } = useSuspenseQuery(
    personalizationPageQuery("bond-details", {
      bond_id: params.bond_id,
    })
  );
  const { data: bondDetails } = useSuspenseQuery(
    createBondDetailsQueryOptions(params.bond_id!)
  );
  return (
    <>
      <Head
        title={`${bondDetails.aboutTheInstitution?.title} - Stable Bonds`}
      />
      {buildPersonalizationWidget(pageResponse.root)}
    </>
  );
}

export function BondDetailsPage() {
  const isDesktop = useMediaQuery("(min-width: 768px)");
  return isDesktop ? <DesktopPage /> : <MobilePage />;
}
