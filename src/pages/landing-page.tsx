import Cover from "./landing-page/components/cover";
import FaqSupport from "./landing-page/components/faq-support";
import Footer from "./landing-page/components/footer";
import Head from "@/components/ui/head";
import HighReturns from "./landing-page/components/high-returns-statement";
import Shield from "./landing-page/components/shield";
import KnowMore from "./landing-page/components/know-more";
import Testimonial from "./landing-page/components/testimonial/testimonial";
import BackedByBest from "./landing-page/components/backed-by-best/backed-by-best";
import YieldMaturityCalculator from "./landing-page/components/yield-maturity-calculator";
import Button from "@/components/ui/button/button";
import { useRef } from "react";
import LandingPageFloatingFooter from "@/components/ui/landing-page-floating-footer";
import { queryClient } from "@/queries/client";
import { queryOptions } from "@tanstack/react-query";
import { redirect } from "react-router";
import { authQueryOptions } from "@/queries/auth";

export async function loader() {
  const authTokenQuery = queryOptions(authQueryOptions());
  const token = await queryClient.fetchQuery(authTokenQuery);
  if (token) {
    return redirect("/home");
  }
}

export default function LandingPage() {
  const shieldRef = useRef<HTMLDivElement>(null);
  const scrollToShield = () => {
    if (shieldRef.current) {
      shieldRef.current.scrollIntoView({
        behavior: "smooth",
        block: "end",
      });
    }
  };
  return (
    <>
      <Head
        title="Invest in Bonds - High Returns"
        ogUrl="https://stablebonds.in"
      />
      <Cover />
      <div className="mx-auto mt-8 mb-12 px-8 md:pt-8 md:pb-[40px]">
        <HighReturns />
      </div>
      <div ref={shieldRef}>
        <Shield />
      </div>
      <div className="px-5 pt-8 pb-[24px] lg:px-60 lg:pt-20 lg:pb-5">
        <KnowMore />
      </div>
      <Testimonial />
      <div className="mx-auto mt-[48px] mb-[48px] max-w-[1366px] px-5 md:mt-27 md:mb-[60px] md:px-15 xl:px-30">
        <BackedByBest />
      </div>
      <div className="mx-auto px-5 md:w-[210px]">
        <Button onClick={scrollToShield}>View all bonds</Button>
      </div>
      <FaqSupport />
      <div className="px-5 sm:hidden">
        <YieldMaturityCalculator />
      </div>
      <Footer />
      <div className="sm:hidden">
        <LandingPageFloatingFooter
          children={
            <Button href="/authentication/mobile-number">Get started </Button>
          }
        />
      </div>
    </>
  );
}
