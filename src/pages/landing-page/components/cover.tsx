import HomepageHeader from "./homepage-header";
import underlineImage from "@/assets/images/icons/brown-underline.svg";
import classes from "./home.module.css";
import patchGradient from "@/assets/images/illustrations/patch_gradient_2july.webp";
import clsx from "clsx";
import DesktopCarousel from "./desktop-carousel";
import MobileCarousel from "./mobile-carousel";
import { useQuery } from "@tanstack/react-query";
import { createCollectionQueryOptions } from "@/queries/bonds";

export default function Cover() {
  const query = useQuery(
    createCollectionQueryOptions("popular_bonds_web", {
      media_items: "MastHead",
    })
  );

  return (
    <div className={classes.coloredBackground}>
      <HomepageHeader />
      <div
        className={clsx(classes.coloredBackground, "relative pb-6 md:pb-20")}
      >
        <section className="mx-auto max-w-[1366px]">
          <div className="space-y-5 px-5 md:px-20">
            <div className="flex flex-col gap-10 pt-20 pb-6 md:pt-20 md:pb-9">
              <div className="space-y-6 md:space-y-10">
                <h1 className="text-center text-[30px] leading-[40px] tracking-[-2px] md:text-[54px] md:leading-[72px]">
                  Invest in bonds & earn
                  <br />
                  <span className="relative font-serif font-medium italic">
                    9 <span className="text-[22px] md:text-[40px]">to</span> 12%
                    <img
                      src={underlineImage}
                      alt="Underline"
                      className="absolute right-0 -bottom-1 left-0"
                      style={{
                        width: "-webkit-fill-available",
                      }}
                    />
                  </span>
                  &nbsp;<span>returns</span>
                </h1>
                <img
                  src="https://assets.stablemoney.in/web-frontend/website/sebi_regulated_platform.webp"
                  alt="Regulated"
                  className="mx-auto w-44 md:w-65"
                />
              </div>
            </div>
          </div>
          <div className="relative z-40 hidden md:block">
            <DesktopCarousel query={query} />
          </div>
          <div className="relative z-40 md:hidden">
            <MobileCarousel query={query} />
          </div>
        </section>
        <img
          src={patchGradient}
          alt="Glow"
          className="absolute -bottom-6 left-0 w-[70%] max-sm:opacity-80 md:-bottom-17 md:w-[50%]"
        />
      </div>
    </div>
  );
}
