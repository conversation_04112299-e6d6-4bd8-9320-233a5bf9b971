import Button from "@/components/ui/button/button";
import clsx from "clsx";
import { useUserQuery } from "@/hooks/user";
import { logout } from "@/clients/auth";
import { useEffect, useState } from "react";
import { useViewEvent } from "@/hooks/view-event";
import HamburgerMenu from "./hamburger-menu";
import menu from "@/assets/images/icons/cil-menu.svg";
interface HomepageHeaderProps {
  className?: string;
}

export default function HomepageHeader({ className }: HomepageHeaderProps) {
  const userQuery = useUserQuery();
  const isLoggedIn = !!userQuery.data;
  const [isScrolled, setIsScrolled] = useState(false);
  const { ref } = useViewEvent({
    eventName: "bond_home_page_desktop",
  });

  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      setIsScrolled(scrollTop > 0);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);
  return (
    <>
      <div
        className={clsx(
          "fixed top-0 z-60 w-full transform border-b border-white/30 px-5 transition-colors duration-300",
          isScrolled ? "bg-[#120b00]" : "bg-transparent",
          className
        )}
        ref={ref}
      >
        <div className="container mx-auto flex h-16 w-full items-center justify-between">
          <img
            src="https://assets.stablemoney.in/web-frontend/stable_bonds_white_logo.webp"
            alt="logo"
            className="h-6 md:h-8"
          />
          <div className="flex gap-2">
            <div className="flex items-center gap-2">
              {!isLoggedIn ? (
                <>
                  <Button
                    size="small"
                    href="/authentication/mobile-number"
                    variant="secondary"
                  >
                    <span className="max-sm:text-[12px]">Login/Register</span>
                  </Button>
                </>
              ) : (
                <>
                  <Button
                    size="small"
                    href="/profile"
                    className="hidden shrink-0 md:flex"
                    variant="secondary"
                  >
                    Profile
                  </Button>
                  <Button
                    size="small"
                    href="/investments"
                    className="hidden shrink-0 md:flex"
                    variant="secondary"
                  >
                    Passbook
                  </Button>
                  <Button
                    size="small"
                    type="button"
                    onClick={logout}
                    className="hidden shrink-0 md:flex"
                    variant="secondary"
                  >
                    Log out
                  </Button>
                </>
              )}
              <HamburgerMenu>
                <img src={menu} alt="hamburger" className="h-4 w-4" />
              </HamburgerMenu>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
