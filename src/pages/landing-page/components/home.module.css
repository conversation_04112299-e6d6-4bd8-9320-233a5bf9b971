@keyframes gradientRotate {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

.coloredBackground {
  background: linear-gradient(180deg, #120b00 0%, #744f18 100%);
  background-repeat: repeat;
  background-size: cover;
  color: white;
  strong {
    font-weight: 500;
  }
}

.footerBackground {
  background: #120b00;
  background-repeat: repeat;
  background-size: cover;
  color: white;
  strong {
    font-weight: 500;
  }
}

.legal {
  color: rgba(255, 255, 255, 0.5);
  p,
  ol {
    margin-bottom: 12px;
  }
  a {
    color: rgba(255, 255, 255, 0.8);
  }
}

.importantLinks {
  a {
    border-right: 1px solid rgba(255, 255, 255, 0.5);
    padding: 0 5px;
  }
  a:last-child {
    border-right: none;
  }
}
