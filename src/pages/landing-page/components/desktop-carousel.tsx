import blackArrowIcon from "@/assets/images/icons/black-left-carousel.svg";
import {
  StackedCarousel,
  ResponsiveContainer,
} from "react-stacked-center-carousel";
import QueryRenderer from "@/components/functional/query-renderer";
import React, { useRef } from "react";
import type { slideProp } from "react-stacked-center-carousel/dist/interfaces";
import clsx from "clsx";
import Pagination from "@/components/ui/pagination/pagination";
import type { CollectionResponse } from "@/clients/gen/broking/Collection_pb";
import type { UseQueryResult } from "@tanstack/react-query";
import CarouselBondCard from "@/components/bonds/carousel-bond-card";

function NextEl() {
  return (
    <img
      src={blackArrowIcon}
      alt="left carousel"
      className="h-[44px] w-[44px] transform cursor-pointer"
    />
  );
}

function PrevEl() {
  return (
    <img
      src={blackArrowIcon}
      alt="left carousel"
      className="h-[44px] w-[44px] rotate-180 transform cursor-pointer"
    />
  );
}

const Card = React.memo(function (props: slideProp) {
  const { data, dataIndex } = props;
  const item = data[dataIndex];
  return (
    <CarouselBondCard
      item={item}
      className={clsx(
        "inline-block h-45 w-75 shrink-0 md:h-97 md:w-136",
        !props.isCenterSlide && "scale-95 md:opacity-80",
        props.isCenterSlide && "scale-95"
      )}
    />
  );
});

export default function DesktopCarousel({
  query,
}: {
  query: UseQueryResult<CollectionResponse, Error>;
}) {
  const ref = useRef<StackedCarousel>(undefined);
  const [centerSlideDataIndex, setCenterSlideDataIndex] = React.useState(0);
  const onCenterSlideDataIndexChange = (newIndex: number) => {
    setCenterSlideDataIndex(newIndex);
  };
  return (
    <QueryRenderer query={query}>
      {(collection) => (
        <div className="w-full">
          <ResponsiveContainer
            carouselRef={ref}
            render={(parentWidth, carouselRef) => {
              let slideWidth = parentWidth;
              if (parentWidth < 380) {
                slideWidth = 300;
              } else if (parentWidth > 380 && parentWidth <= 390) {
                slideWidth = 320;
              } else if (parentWidth > 390 && parentWidth <= 400) {
                slideWidth = 320;
              } else if (parentWidth > 400 && parentWidth < 800) {
                slideWidth = 340;
              } else {
                slideWidth = 550;
              }
              return (
                <StackedCarousel
                  ref={carouselRef}
                  slideComponent={Card}
                  slideWidth={slideWidth}
                  height={parentWidth < 800 ? 250 : 400}
                  carouselWidth={parentWidth}
                  data={collection.collectionItem}
                  maxVisibleSlide={3}
                  disableSwipe={parentWidth > 800}
                  onActiveSlideChange={onCenterSlideDataIndexChange}
                  swipeSpeed={0.475}
                />
              );
            }}
          />

          <Pagination
            totalSlides={collection.collectionItem.length}
            activeIndex={centerSlideDataIndex}
            className="relative top-[5px] justify-center sm:top-[10px]"
          />
          <>
            <button
              style={{
                position: "absolute",
                top: "40%",
                left: 60,
                touchAction: "manipulation",
              }}
              className="hidden md:block"
              onClick={() => {
                ref.current?.goBack();
              }}
              onTouchStart={(e) => e.preventDefault()}
            >
              <PrevEl />
            </button>
            <button
              style={{
                position: "absolute",
                top: "40%",
                right: 60,
                touchAction: "manipulation",
              }}
              className="hidden md:block"
              onClick={() => {
                ref.current?.goNext();
              }}
              onTouchStart={(e) => e.preventDefault()}
            >
              <NextEl />
            </button>
          </>
        </div>
      )}
    </QueryRenderer>
  );
}
