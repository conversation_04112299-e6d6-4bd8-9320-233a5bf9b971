import Avatar from "@/components/ui/avatar";
import { useId } from "react";

export default function TestimonialCard({
  name,
  location,
  comment,
  pictureUrl,
}: {
  name: string;
  location: string;
  comment: string;
  pictureUrl?: string;
}) {
  return (
    <div>
      <div className="border-black-5 flex h-full w-[350px] items-start rounded-xl border-[1px] bg-white p-6 shadow-[0_0_8px_rgba(0,0,0,0.05)] transition-shadow duration-100 ease-linear md:w-[539px]">
        <div className="flex w-full flex-col gap-[14px]">
          <div className="flex items-center justify-between gap-2">
            <div className="flex items-center gap-[14px]">
              <Avatar src={pictureUrl} name={name} id={useId()} />
              <div className="flex flex-col">
                <p className="text-black-80 text-[13px] md:text-[20px]">
                  {name}
                </p>
                <p className="text-black-50 text-[10px] md:text-[15px]">
                  {location}
                </p>
              </div>
            </div>
          </div>
          <p className="text-black-60 line-clamp-3 overflow-hidden text-[11px] md:text-[18px]">
            {comment}
          </p>
        </div>
      </div>
    </div>
  );
}
