import clsx from "clsx";
import classes from "./testimonial.module.css";
import { testimonialFirstRow, testimonialSecondRow } from "./testimonial-data";
import TestimonialCard from "./testimonial-card";

export default function Testimonial() {
  return (
    <div className={classes.dottedBackground}>
      <div className="flex flex-col pt-[24px] text-center md:pt-23">
        <p className="text-black-80 text-[26px] tracking-[-1px] md:text-[48px] md:tracking-[-2px]">
          Take{" "}
          <span className="font-serif font-medium text-[#B67E25] italic">
            their word
          </span>{" "}
          for it!
        </p>
      </div>
      <div className={classes.testimonialContainer}>
        <div
          className={clsx(
            classes.TestimonialRowContainer,
            classes.left,
            "flex w-fit gap-6 pr-6 md:gap-3"
          )}
        >
          {testimonialFirstRow.map((testimonial) => (
            <TestimonialCard
              key={testimonial.userName}
              name={testimonial.userName}
              location={testimonial.userLocation}
              comment={testimonial.userTestimonial}
              pictureUrl={testimonial.userProfileImage}
            />
          ))}
        </div>
        <div
          className={clsx(
            classes.TestimonialRowContainer,
            classes.reverse,
            "reverse flex w-fit gap-6 pr-6 md:gap-3"
          )}
        >
          {testimonialSecondRow.map((testimonial) => (
            <TestimonialCard
              key={testimonial.userName}
              name={testimonial.userName}
              location={testimonial.userLocation}
              comment={testimonial.userTestimonial}
              pictureUrl={testimonial.userProfileImage}
            />
          ))}
        </div>
      </div>
    </div>
  );
}
