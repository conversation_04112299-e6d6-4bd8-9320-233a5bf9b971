import { useQuery } from "@tanstack/react-query";
import QueryRenderer from "@/components/functional/query-renderer";
import Accordion from "@/components/ui/accordion/accordion";
import AccordionItem from "@/components/ui/accordion/accordion-item";
import LinkButton from "@/components/ui/button/link-button";
import AdaptiveModal from "@/components/ui/adaptive-modal/adaptive-modal";
import Surface from "@/components/ui/surface/surface";
import { getFAQsQueryOptions } from "@/queries/business";
import AllFaqs from "./all-faqs";
import { trackEvent } from "@/utils/analytics";
import YieldMaturityCalculator from "./yield-maturity-calculator";

export default function FaqSupport() {
  const query = useQuery(
    getFAQsQueryOptions({
      identifier: "BOND_LANDING_PAGE_FAQ",
      namespace: "BONDS_HOME",
    })
  );

  return (
    <>
      <div className="mb-4 w-screen">
        <div className="mx-auto mt-[40px] max-w-[1366px] px-5 md:mt-[72px] md:px-20">
          <div className="hidden md:block">
            <p className="heading mb-8">Frequently asked questions</p>
          </div>
          <div className="flex flex-col-reverse gap-[48px] md:flex-row">
            <div className="flex w-full flex-col justify-evenly">
              <p className="heading mb-4 md:hidden">
                Frequently asked questions
              </p>
              <QueryRenderer query={query}>
                {({ faqs }) => (
                  <Surface elevation="md">
                    <Accordion>
                      {faqs.slice(0, 5).map((faq, index) => {
                        const handleClick = () =>
                          trackEvent("generic_faq_card_clicked", {
                            tag: "BONDS_HOME",
                            faq_question: faq.question,
                            faq_number: index + 1,
                          });
                        return (
                          <div key={faq.question} onClick={handleClick}>
                            <AccordionItem label={faq.question}>
                              <div
                                dangerouslySetInnerHTML={{ __html: faq.answer }}
                              />
                            </AccordionItem>
                          </div>
                        );
                      })}
                    </Accordion>
                    <div className="pb-5 text-center">
                      <AdaptiveModal
                        trigger={<LinkButton>View all</LinkButton>}
                        header={
                          <h2 className="text-heading2 mb-4 px-5 md:mt-4">
                            All FAQs
                          </h2>
                        }
                      >
                        {() => <AllFaqs />}
                      </AdaptiveModal>
                    </div>
                  </Surface>
                )}
              </QueryRenderer>
              <div className="hidden pt-6 sm:block">
                <YieldMaturityCalculator />
              </div>
            </div>
            <div className="flex flex-shrink-0 flex-col">
              <p className="heading mb-4 md:hidden">Reach out to us</p>
              <a href="tel:+918045889087">
                <img
                  src="https://assets.stablemoney.in/web-frontend/web-app/CallerMen.webp"
                  alt="Caller"
                  className="w-[420px] md:w-[300px] lg:w-[457px]"
                />
              </a>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

const styles = `
.heading {
    font-size: 32px;
    line-height: 44px;
    color: rgba(0, 0, 0, 0.8);
    width: 100%;
    letter-spacing: -1px;
    -webkit-font-smoothing: antialiased;
}

@media screen and (max-width: 471px) {
    .heading {
        font-size: 18px;
        font-weight: 400;
        line-height: 26px;
        text-align: left;
        text-underline-position: from-font;
        margin-bottom: 16px;
    }
}
`;

// Inject styles
if (typeof document !== "undefined") {
  const styleSheet = document.createElement("style");
  styleSheet.textContent = styles;
  document.head.appendChild(styleSheet);
}
