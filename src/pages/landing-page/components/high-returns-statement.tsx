import purpleSheild from "@/assets/images/icons/black-shield.svg";
import purpleStar from "@/assets/images/icons/black-star.svg";
import purpleMembers from "@/assets/images/icons/black-members.svg";

export default function HighReturns() {
  return (
    <div className="space-y-10 md:space-y-18">
      <div className="max-sm:mb-8">
        <div className="text-center font-serif text-[24px] leading-[38px] font-medium tracking-[-0.4px] italic md:text-[54px] md:leading-[68px] md:tracking-[-2px]">
          <p className="text-black-80">
            <span className="text-[#B67E25]"> Higher returns </span>than FD,
          </p>
          <p>
            <span className="text-[#B67E25]">Less risky </span> than equity
          </p>
        </div>
      </div>
      <div className="mx-auto flex flex-col gap-8 md:flex-row md:justify-center md:gap-12">
        <div className="flex items-center gap-3 md:flex-col">
          <img
            src={purpleSheild}
            alt="Purple Shield"
            className="h-13 w-13 md:h-18 md:w-18"
          />
          <div className="md:space-y-5 md:text-center">
            <p className="text-black-80 text-[18px] tracking-[-0.5px] md:text-[28px]">
              Monthly payout options
            </p>
            <p className="text-black-50 text-[13px] tracking-tighter md:w-80 md:text-[22px]">
              Get fixed returns credited every month.
            </p>
          </div>
        </div>
        <div className="flex items-center gap-3 md:flex-col">
          <img
            src={purpleStar}
            alt="Purple Star"
            className="h-13 w-13 md:h-18 md:w-18"
          />
          <div className="md:space-y-5 md:text-center">
            <p className="text-black-80 text-[18px] tracking-[-0.5px] md:text-[28px]">
              Secured bonds
            </p>
            <p className="text-black-50 text-[13px] tracking-tighter md:w-80 md:text-[22px]">
              Your investment is backed by assets.
            </p>
          </div>
        </div>
        <div className="flex items-center gap-3 md:flex-col">
          <img
            src={purpleMembers}
            alt="Purple Members"
            className="h-13 w-13 md:h-18 md:w-18"
          />
          <div className="md:space-y-5 md:text-center">
            <p className="text-black-80 text-[18px] tracking-[-0.5px] md:text-[28px]">
              Not linked with stock market
            </p>
            <p className="text-black-50 text-[13px] tracking-tighter md:w-80 md:text-[22px]">
              Unaffected by market ups and downs.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
