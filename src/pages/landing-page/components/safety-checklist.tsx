import clsx from "clsx";
import ChecklistCard from "./checklist-card";
import classes from "./safety-checklist.module.css";

const data = [
  {
    title: "0 defaults till date",
    subtitle:
      "Since launch, all bonds on Stable Bonds have had a 100% no-default record.",
    img_src:
      "https://assets.stablemoney.in/web-frontend/web-app/shield-black.webp",
  },
  {
    title: "Listed bonds",
    subtitle: "All bonds on Stable Bonds are listed on the stock exchanges.",
    img_src:
      "https://assets.stablemoney.in/web-frontend/web-app/listed-bonds.webp",
  },
  {
    title: "Strong leadership",
    subtitle:
      "Consistent performance driven by experienced CEOs make these a safe bet.",
    img_src:
      "https://assets.stablemoney.in/web-frontend/web-app/leadership.webp",
  },
];

export default function SafetyChecklist() {
  return (
    <div className="w-screen">
      <div className="m-auto mt-12 max-w-[1366px] px-5 md:px-20">
        <div className="mb-8 flex w-full flex-col justify-evenly">
          <h2 className={clsx("mb-4 md:mb-8", classes.heading)}>
            Stable Bonds Safety Checklist
          </h2>
          <div className="flex flex-wrap gap-3 md:flex-nowrap md:gap-6">
            {data.map((bond) => (
              <ChecklistCard
                key={bond.title}
                title={bond.title}
                subtitle={bond.subtitle}
                imgSrc={bond.img_src}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
