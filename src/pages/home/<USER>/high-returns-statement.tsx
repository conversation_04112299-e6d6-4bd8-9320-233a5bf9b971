import purpleShield from "@/assets/images/icons/black-shield.svg";
import purpleStar from "@/assets/images/icons/black-star.svg";
import purpleMembers from "@/assets/images/icons/black-members.svg";

export default function HighReturns() {
  return (
    <div className="space-y-10 md:space-y-14">
      <div className="text-center font-serif text-[24px] leading-[38px] font-medium tracking-[-0.4px] italic md:text-[36px] md:tracking-[-2px]">
        <p className="text-black-80">
          <span className="text-[#B67E25]"> Higher returns </span>than FD,
        </p>
        <p>
          <span className="text-[#B67E25]">Less risky </span> than equity
        </p>
      </div>
      <div className="mx-auto flex justify-evenly">
        <div className="flex items-center gap-3 md:flex-col">
          <img
            src={purpleShield}
            alt="Purple Shield"
            className="h-[72px] w-[72px]"
          />
          <div className="md:space-y-2 md:text-center">
            <p className="text-black-80 text-[18px] tracking-[-0.5px]">
              Monthly payout <br />
              options
            </p>
            <p className="text-black-50 w-49 text-[15px] tracking-tighter">
              Get fixed returns credited every month.
            </p>
          </div>
        </div>
        <div className="flex items-center gap-3 md:flex-col">
          <img
            src={purpleStar}
            alt="Purple Star"
            className="h-[72px] w-[72px]"
          />
          <div className="md:space-y-2 md:text-center">
            <p className="text-black-80 text-[18px] tracking-[-0.5px]">
              Secured <br /> bonds
            </p>
            <p className="text-black-50 w-49 text-[15px] tracking-tighter">
              Your investment is backed by assets.
            </p>
          </div>
        </div>
        <div className="flex items-center gap-3 md:flex-col">
          <img
            src={purpleMembers}
            alt="Purple Members"
            className="h-[72px] w-[72px]"
          />
          <div className="md:space-y-2 md:text-center">
            <p className="text-black-80 text-[18px] tracking-[-0.5px]">
              Not linked with <br /> stock market
            </p>
            <p className="text-black-50 w-49 text-[15px] tracking-tighter">
              Unaffected by market ups and downs.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
