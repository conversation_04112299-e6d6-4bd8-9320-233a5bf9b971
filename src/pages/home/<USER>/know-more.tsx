import Animation from "@/components/ui/animation/animation";
import Surface from "@/components/ui/surface/surface";
import tick from "@/assets/images/icons/tick.svg";
import Button from "@/components/ui/button/button";

export default function KnowMore() {
  return (
    <div className="flex flex-col items-center justify-center gap-4 lg:flex-row lg:justify-between">
      <div className="text-center lg:text-left">
        <div className="space-y-[40px]">
          <div>
            <p className="text-black-80 text-[32px] leading-12">
              <span className="font-serif font-medium tracking-[-1px] text-[#B67E25] italic">
                Know more
              </span>
              <span className="tracking-[-1px]"> about bonds</span>
            </p>
          </div>
          <div className="space-y-6 text-[15px]">
            <div className="flex items-center gap-2">
              <img src={tick} alt="Tick" />
              <p className="text-black-80 tracking-[-0.2px]">
                <span className="font-medium"> SEBI regulated platform</span>
                <span className="text-black-50">, secured bonds</span>
              </p>
            </div>
            <div className="flex items-center gap-2">
              <img src={tick} alt="Tick" />
              <p className="text-black-80 tracking-[-0.2px]">
                <span className="text-black-50">Earn</span>
                <span className="font-medium"> fixed returns</span>
                <span className="text-black-50">, not linked to markets</span>
              </p>
            </div>
            <div className="flex items-center gap-2">
              <img src={tick} alt="Tick" />
              <p className="text-black-80 tracking-[-0.2px]">
                <span className="text-black-50">Start with</span>
                <span className="font-medium"> as low as ₹100</span>
              </p>
            </div>
          </div>
        </div>

        <div className="hidden w-34 pt-8 lg:block">
          <Button className="hidden">
            <p className="text-[15px] tracking-[-0.3px]"> View all bonds </p>
          </Button>
        </div>
      </div>
      <Surface
        elevation="md"
        className="max-w-[300px] flex-shrink-0 max-lg:mx-auto"
      >
        <div className="p-5">
          <Animation
            src="https://assets.stablemoney.in/app/bonds_explainer_19mb_final_26_jun.mp4"
            type="video"
            playOnVisibility
            loop
          />
        </div>
      </Surface>
    </div>
  );
}
