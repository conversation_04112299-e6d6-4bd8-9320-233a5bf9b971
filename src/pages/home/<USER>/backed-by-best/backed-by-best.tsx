import purpleUnderline from "@/assets/images/icons/brown-underline.svg";
import classes from "./backed-by-best.module.css";

type RenderCardProps = {
  name: string;
  designation: string;
  investorImage: string;
  companyImage: string;
};

function RenderInvestor({
  name,
  designation,
  investorImage,
  companyImage,
}: RenderCardProps) {
  return (
    <div className="flex flex-col items-center gap-6 rounded-xl">
      <img src={investorImage} alt={name} className="h-[178px] md:w-[178px]" />

      <div className="space-y-2 md:space-y-4">
        <div>
          <img src={companyImage} alt={name} className="m-auto h-10 w-auto" />
        </div>
        <div className="flex flex-col items-start md:items-center md:justify-center">
          <p className="text-black-80 text-center text-[18px]">{name}</p>
          <p className="text-black-50 w-[178px] text-center text-[18px]">
            {designation}
          </p>
        </div>
      </div>
    </div>
  );
}

export default function BackedByBest() {
  const data = [
    {
      investorImage:
        "https://assets.stablemoney.in/web-frontend/website/nandan.webp",
      investorCompanyImage:
        "https://assets.stablemoney.in/web-frontend/website/infosys.webp",
      name: "Nandan Nilekani",
      designation: "Non-executive chairman of Infosys",
    },
    {
      investorImage:
        "https://assets.stablemoney.in/web-frontend/website/kunal.webp",
      investorCompanyImage:
        "https://assets.stablemoney.in/web-frontend/website/snapdeal.webp",
      name: "Kunal Bahl",
      designation: "Former CEO, Snapdeal",
    },
    {
      investorImage:
        "https://assets.stablemoney.in/web-frontend/website/sriharsha.webp",
      investorCompanyImage:
        "https://assets.stablemoney.in/web-frontend/website/swiggy.webp",
      name: "Sriharsha Majety",
      designation: "Co-Founder, Swiggy",
    },
  ];
  return (
    <div className="flex w-full flex-col justify-evenly gap-8 md:gap-14">
      <div className="flex items-center justify-center gap-4">
        <div className="h-[1px] w-[187px] bg-linear-to-r from-[#FFFFFF00] to-[#AFAFAF94]" />
        <h2
          className={
            classes.headingReklesss +
            " text-black-80 flex-shrink-0 text-center font-medium"
          }
        >
          <span>Backed by</span>
          &nbsp;
          <span
            className={classes.headingReklesss + " relative text-[#B08032]"}
          >
            the best
            <img
              src={purpleUnderline}
              alt=""
              className="absolute left-0 h-1.5 md:h-4"
            />
          </span>
        </h2>
        <div className="h-[1px] w-[187px] bg-linear-to-r from-[#AFAFAF94] to-[#FFFFFF00]" />
      </div>
      <div className="flex flex-col items-center gap-10 md:gap-12">
        <div className="flex flex-wrap justify-center md:gap-[61px]">
          {data.map((bond) => {
            return (
              <RenderInvestor
                name={bond.name}
                designation={bond.designation}
                investorImage={bond.investorImage}
                companyImage={bond.investorCompanyImage}
                key={bond.name}
              />
            );
          })}
        </div>
        <img
          src="https://assets.stablemoney.in/app/web_angels_25june.webp"
          className="w-[628px]"
        />
      </div>
    </div>
  );
}
