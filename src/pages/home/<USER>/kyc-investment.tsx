import { useQuery } from "@tanstack/react-query";
import QueryRenderer from "@/components/functional/query-renderer";
import Surface from "@/components/ui/surface/surface";
import { getBondsNetworthQueryOptions } from "@/queries/bonds";
import { formatCurrency } from "@/utils/format";
import SectionHeading from "@/components/ui/section-heading/section-heading";
import chevron from "@/assets/images/icons/chevron.svg";
import Nudges from "@/components/functional/desktop-nudges/nudges";

export default function InvestmentsKycSummary() {
  const query = useQuery(getBondsNetworthQueryOptions());

  return (
    <QueryRenderer query={query}>
      {(networth) => (
        <div className="w-full space-y-4">
          <div className="-translate-y-1">
            <SectionHeading title="My investments" size="medium" />
          </div>
          <Surface elevation="md" className="mb-4">
            <Nudges pageUri="bonds-home" />
            <div className="space-y-8 p-8">
              <a className="flex w-full justify-between" href="/investments">
                <span className="text-title-all-caps font-medium text-black">
                  passbook
                </span>

                <img
                  src={chevron}
                  alt="chevron-right"
                  className="text-black-50 h-4 w-4"
                />
              </a>
              <div className="space-y-6">
                <div className="text-body1 flex w-full justify-between">
                  <p className="text-black-50">Bonds net worth</p>
                  <p className="text-black-80 tracking font-medium">
                    {formatCurrency(
                      networth.totalInvestment + networth.currentGains
                    )}
                  </p>
                </div>
                <div className="text-body1 flex w-full justify-between">
                  <p className="text-black-50">Overall gains</p>
                  {networth.currentGains === 0 ? (
                    <p className="text-black-80 tracking font-medium">
                      {formatCurrency(networth.currentGains)}
                    </p>
                  ) : (
                    <p className="text-green tracking font-medium">
                      + {formatCurrency(networth.currentGains)}
                    </p>
                  )}
                </div>
              </div>
            </div>
          </Surface>
        </div>
      )}
    </QueryRenderer>
  );
}
