import FormPage from "@/components/bonds/form-page";
import Button from "@/components/ui/button/button";
import Input from "@/components/ui/input/input";
import { Formik, Field } from "formik";
import { authenticate, initiateAuth } from "@/queries/auth";
import { useSearchParams } from "react-router";
import useNavigate from "@/hooks/navigate";
import { useEffect } from "react";
import Head from "@/components/ui/head";
import { validationSchema, type FormValues } from "./schema";
import { getHomeUrl } from "@/utils/routing";
import { getErrorMessage, withErrorToast } from "@/utils/errors";
import { HttpCallException } from "@/exceptions/http-call-exception";
import { getPathForNextAlphaStep } from "@/utils/onboarding-routes";
import { trackEvent } from "@/utils/analytics";
import Form from "@/components/functional/form";
import { getConfig } from "@/queries/identity";
import { AppConfigType } from "@/clients/gen/platform/public/models/identity/Common_pb";
import { aesGcmEncrypt } from "@/utils/browser-encryption";
import { useMachine } from "@xstate/react";
import { countDownTimerMachine } from "../../../machines/count-down-timer-machine";
import clsx from "clsx";

export default function MobileOtpPage() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [state, send] = useMachine(countDownTimerMachine, {
    input: { duration: 30 },
  });
  const userId = searchParams.get("userId");
  const challenge = searchParams.get("challenge");
  const mobileNumber = searchParams.get("mobileNumber") ?? "";

  useEffect(() => {
    send({ type: "START", duration: 30 });
  }, [send]);

  const resendOtp = async () => {
    if (state.matches("idle") || state.matches("finished")) {
      const config = await getConfig();
      const publicKey =
        config.data
          .find(
            (config) =>
              config.configName === AppConfigType.ENCRYPTION_PUBLIC_KEY
          )
          ?.configValue.replace(/-----.*?-----/g, "")
          .replace(/[\n\r\s]/g, "")
          .trim() ?? "";
      const [encryptedMobile, aesKey] = await aesGcmEncrypt(
        mobileNumber,
        publicKey
      );
      send({ type: "START", duration: 30 });
      await withErrorToast(() =>
        initiateAuth(mobileNumber, encryptedMobile, aesKey)
      )();
    }
  };

  // Redirect if required parameters are missing
  useEffect(() => {
    if (!userId || !challenge || !mobileNumber) {
      navigate("/authentication/mobile-number");
    }
  }, [userId, challenge, mobileNumber, navigate]);

  if (!userId || !challenge || !mobileNumber) {
    return null;
  }

  return (
    <>
      <Head title="OTP Verification" />
      <Formik
        initialValues={{} as FormValues}
        validationSchema={validationSchema}
        validateOnMount
        onSubmit={withErrorToast(async function handleSubmit(
          values,
          { setFieldError }
        ) {
          try {
            const { alphaStatus } = await authenticate(
              challenge,
              userId,
              values.answer
            );
            trackEvent("mobile_otp_verified", { mobileNumber });
            if (alphaStatus.next) {
              navigate(getPathForNextAlphaStep(alphaStatus.next), {
                replace: true,
              });
            } else {
              navigate(getHomeUrl(), { replace: true });
            }
          } catch (error) {
            if (
              error instanceof HttpCallException &&
              error.response.status === 400
            ) {
              return setFieldError("answer", await getErrorMessage(error));
            }
            throw error;
          }
        })}
      >
        {({ isSubmitting, errors, submitCount }) => {
          return (
            <Form id="mobile-otp">
              <FormPage
                title="OTP please?"
                description={`We have sent it to +91 ${mobileNumber}`}
                footer={
                  <Button
                    type="submit"
                    loading={isSubmitting}
                    form="mobile-otp"
                  >
                    Submit OTP
                  </Button>
                }
              >
                <Field
                  name="answer"
                  label="OTP"
                  as={Input}
                  error={submitCount > 0 ? errors.answer : undefined}
                  maxLength={6}
                />
                <div className="flex gap-1">
                  <button
                    onClick={resendOtp}
                    disabled={state.matches("running")}
                    className={clsx(
                      "text-body1 link-button inline-flex cursor-default items-center justify-center gap-0.5 underline underline-offset-4",
                      {
                        "text-black-50": state.matches("running"),
                        "text-purple cursor-pointer": !state.matches("running"),
                      }
                    )}
                    type="button"
                  >
                    {state.matches("running") ? "Resend in" : "Resend OTP"}
                  </button>
                  {state.matches("running") && (
                    <span className="text-body1">
                      {Math.floor(state.context.current / 60)
                        .toString()
                        .padStart(2, "0")}
                      :
                      {(state.context.current % 60).toString().padStart(2, "0")}
                    </span>
                  )}
                </div>
              </FormPage>
            </Form>
          );
        }}
      </Formik>
    </>
  );
}
